{"version": "2.0.0", "tasks": [{"type": "flutter", "command": "flutter", "args": ["pub", "run", "build_runner", "build", "--delete-conflicting-outputs"], "problemMatcher": ["$dart-build_runner"], "group": "build", "label": "flutter: flutter pub run build_runner build --delete-conflicting-outputs", "detail": ""}, {"type": "dart", "command": "dart", "args": ["run", "intl_utils:generate"], "problemMatcher": ["$dart-build_runner"], "group": "build", "label": "dart run intl_utils:generate", "detail": ""}, {"type": "flutter", "command": "flutter", "args": ["build", "apk"], "group": "build", "problemMatcher": [], "label": "flutter: flutter build apk", "detail": ""}, {"type": "flutter", "command": "flutter", "args": ["build", "apk", "--release"], "group": "build", "problemMatcher": [], "label": "flutter: flutter build apk --release", "detail": ""}, {"type": "flutter", "command": "flutter", "args": ["build", "ios", "--release"], "group": "build", "problemMatcher": [], "label": "flutter: flutter build ios --release", "detail": ""}, {"type": "flutter", "command": "flutter", "args": ["build", "appbundle"], "group": "build", "problemMatcher": [], "label": "flutter: flutter build appbundle", "detail": ""}]}