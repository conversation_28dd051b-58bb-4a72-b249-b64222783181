plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "xyz.cussme.app"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    defaultConfig {
        applicationId = "xyz.cussme.app"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        create("release") {
            storeFile = file("../../sign-keys/android/release-key")
            storePassword = "zcj;K2Vy,km(YJw`\"DuT$7"
            keyAlias = "CussMe"
            keyPassword = "zcj;K2Vy,km(YJw`\"DuT$7"
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release") {
                manifestPlaceholders.putAll(
                    mapOf(
                        "facebookAppId" to "1848050979365733",
                        "facebookProtocolScheme" to "fb1848050979365733",
                        "facebookClientToken" to "********************************"
                    )
                )
            }
            isShrinkResources = true
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
        debug {
            signingConfig = signingConfigs.getByName("debug") {
                manifestPlaceholders.putAll(
                    mapOf(
                        "facebookAppId" to "2770320903155162",
                        "facebookProtocolScheme" to "fb2770320903155162",
                        "facebookClientToken" to "********************************"
                    )
                )
            }
        }
    }

    buildFeatures {
        viewBinding = true
    }

    lint {
        checkReleaseBuilds = false
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-stdlib:1.9.10")
    implementation("com.google.android.gms:play-services-auth:20.7.0")
}