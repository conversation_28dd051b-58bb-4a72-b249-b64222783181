{"@@locale": "en", "helloWorld": "Hello World!", "signInTitle": "Sign in to <PERSON><PERSON><PERSON>", "signInSubtitle": "You can bookmark/report words, and even suggest edits if you're signed in", "emailLabel": "Email", "passwordLabel": "Password", "forgotPasswordButton": "Forgot password?", "signInButton": "Sign in", "orSignInWith": "or sign in with", "googleButton": "Google", "appleButton": "Apple", "facebookButton": "Facebook", "noAccountText": "Don't have an account? ", "signUpHereText": "Sign up here", "continueWithoutSignIn": "Continue without signing up", "trackingPermissionTitle": "Help us improve your experience", "trackingPermissionMessage": "We'd like to show you personalized ads to support the app. This helps us keep Cussme free for everyone. You can change this anytime in your device settings.", "continueButton": "Continue", "signUpTitle": "Create your account", "signUpSubtitle": "Join <PERSON> to contribute and interact with the community", "confirmPasswordLabel": "Confirm Password", "signUpButton": "Sign up", "orSignUpWith": "or sign up with", "alreadyHaveAccount": "Already have an account? ", "signInHereText": "Sign in here", "passwordRequirements": "Your password needs to be at least 8 characters long and should include at least 1 number and letter", "passwordMismatch": "Passwords do not match", "invalidEmail": "Please enter a valid email address", "signUp": "Sign up", "firstNameLabel": "First name*", "lastNameLabel": "Last name", "emailRequired": "Email*", "passwordRequired": "Password*", "retypePasswordLabel": "Re-type password*", "passwordRequirementsDetailed": "Password must be at least 8 characters long and should include at least 1 number and letter", "firstNameRequired": "First name is required", "emailIsRequired": "Email is required", "invalidEmailFormat": "Invalid email format", "passwordIsRequired": "Password is required", "passwordMinLength": "Password must be at least 8 characters", "passwordNeedsNumber": "Password must contain at least 1 number", "confirmPasswordRequired": "Please confirm your password", "alreadyHaveAccountFull": "Already have an account? Sign in here", "accountCreatedSuccess": "Account created successfully", "invalidInputsErrorMessage": "Some items need attention", "emailEmptyError": "Email is required", "emailInvalidError": "Please enter a valid email address", "passwordEmptyError": "Password is required", "passwordTooShortError": "Password must be at least 8 characters", "passwordNoNumberError": "Password must contain at least 1 number", "passwordNotEnoughLettersError": "Password must contain at least 1 letter", "confirmPasswordEmptyError": "Please confirm your password", "passwordsDoNotMatchError": "Passwords do not match", "isRequiredError": "is required", "somethingWentWrong": "Something went wrong. Please try again.", "failedToSignUp": "Failed to sign up. Please try again.", "failedToSignIn": "Failed to sign in. Please check your credentials and try again.", "failedToInitiateGoogleSignIn": "Failed to initiate Google sign-in. Please try again.", "failedToInitiateAppleSignIn": "Failed to initiate Apple sign-in. Please try again.", "googleSignInNotCompleted": "Google sign-in was not completed. Please try again.", "appleSignInNotCompleted": "Apple sign-in was not completed. Please try again.", "forgotPasswordTitle": "Forgot Password", "forgotPasswordDescription": "Enter your registered email address and we'll send you a reset link.", "sendResetLink": "Send reset link", "resetLinkSentMessage": "An email with a reset link will be sent to your registered email id.", "emailNotFound": "Email not found. Please check your email or sign up.", "resetPassword": "Reset Password", "resetPasswordTitle": "Reset Your Password", "resetPasswordDescription": "Enter a new password for your account.", "accessToken": "Access Token", "noTokenProvided": "No token provided", "passwordResetEmailSent": "Password reset email sent.", "passwordResetCheckSpam": "Please check your spam folder if you haven't received the email in your inbox.", "didNotReceiveEmail": "Did not receive the email?", "resendResetLink": "Resend reset link", "userNotLoggedIn": "You need to be logged in to reset your password.", "providerPasswordResetError": "Reset password is only available for accounts created with an email and password. If you signed up with Google or Facebook, please manage your password through the account provider.", "resendResetLinkTimer": "Request again in {remainTime}", "@resendResetLinkTimer": {"placeholders": {"remainTime": {"type": "String", "example": "01:30"}}}, "enterNewPassword": "Enter new password", "confirmNewPassword": "Confirm new password", "passwordResetSuccessTitle": "Password successfully reset.", "passwordResetSuccessMessage": "You will be logged out and asked to login again to the app.", "goToSignIn": "Go to Sign In", "profileMenuSignIn": "Sign in", "profileMenuAboutApp": "About the app", "profileMenuPreferences": "Preferences", "profileMenuBookmarks": "Bookmarks", "profileMenuLogout": "Logout", "profileMenuGoPremium": "Go premium!", "aboutAppTitle": "About Cussme app", "aboutAppDescription": "The Cussme app was born out of <PERSON><PERSON><PERSON>'s profound need to be a potty-mouthed sicko. \n\nThe goal of the app isn't to pollute people's mind with dirty cuss words, but to foster a sense of learning and awareness of local cultures, slangs, and origins of some of these words.\n\nThis project was completely self-funded, so consider subscribing for the full version if you can.", "developerText": "Developer", "closeButton": "Close", "logoutDialogTitle": "Confirm logout", "logoutDialogMessage": "Are you sure you want to logout?", "cancel": "Cancel", "logout": "Logout", "preferencesTitle": "Preferences", "backButtonText": "Back", "chooseLanguageLabel": "Choose Language", "chooseLanguageSupportingText": "", "chooseApicinessLabel": "Choose Spiciness", "chooseSpicinessSupportingText": "", "accountSettingsLabel": "Account <PERSON><PERSON>", "accountSettingsSupportingText": "Name, email and password", "membershipInfoLabel": "Membership Info", "membershipInfoSubtitle": "Managing your membership", "membershipInfoDescription": "All premium subscriptions are managed by your Google Play or Apple Store account. Please check your subscription settings there to pause, cancel or renew your subscription.", "applyChanges": "Apply", "deleteAccount": "Delete Account", "accountSettingsChangesSaved": "Changes have been saved", "deleteAccountDialogTitle": "Are you sure you want to delete you account?", "deleteAccountDialogMessage": "You will lose access to all bookmarks, preferences and saved data.", "confirmDelete": "Delete", "yesDeleteAccount": "Yes, delete account", "sessionExpiredError": "Your session has expired. Please log in again.", "spicinessMildTitle": "Mild", "spicinessMildDescription": "I'm a child of God", "spicinessCheekyTitle": "Cheeky", "spicinessCheekyDescription": "HR might get involved", "spicinessSpicyTitle": "Spicy", "spicinessSpicyDescription": "The devil hangs his head in shame", "spicinessSlangTitle": "Slang", "spicinessSlangDescription": "Simple slang words for daily conversation", "chooseSpiciness": "Choose Spiciness", "chooseLanguage": "Choose Language", "screenLoadingErrorTitle": "Oops! Something went wrong", "screenLoadingErrorDescription": "Reload the page to try again", "screenLoadingErrorButtonText": "Reload page", "chooseLanguagesForDashboard": "Choose languages you want see in the dashboard", "chooseLanguagesForDashboardHighlight": "languages", "canChangeLanguagesLater": "You can change this later in the settings", "moreLanguagesComingSoon": "More languages support coming soon.", "chooseSpicinessLevel": "Choose the spiciness level of the cuss words", "chooseSpicinessLevelHighlight": "spiciness", "canChangeSpicinessLater": "You can change this later in the settings", "next": "Next", "previous": "Previous", "skip": "<PERSON><PERSON>", "finish": "Finish", "skipToDashboard": "Skip to Dashboard", "deleteAccountSuccess": "You have successfully deleted your account. Please sign up to login again.", "emailAlreadyExists": "This email is already registered with Cussme. Please try signing up with a different email.", "wordOfTheDayFormat": "{language} cuss-word of the Day", "@wordOfTheDayFormat": {"placeholders": {"language": {"type": "String", "example": "English"}}}, "addMoreLanguages": "Add/remove languages", "wordDetailMeaningAndUsage": "Meaning and usage", "wordDetailMeaning": "Meaning", "wordDetailUsage": "Usage", "wordDetailSuggestEdit": "Suggest an edit", "wordDetailSuggestEditHighlight": "edit", "wordDetailReportInaccuracy": "Report as inaccurate", "wordDetailReportInaccuracyHighlight": "Report", "wordDetailBookmarkNotImplemented": "Bookmark feature not implemented yet", "wordDetailPronunciationNotImplemented": "Pronunciation feature not implemented yet", "wordDetailSuggestEditNotImplemented": "Suggest edit feature not implemented yet", "wordDetailReportInaccuracyNotImplemented": "Report inaccuracy feature not implemented yet", "guestBookmarkError": "You need to be signed in to bookmark words", "bookmarkAddedSuccess": "Bookmark added!", "bookmarkRemovedSuccess": "Bookmark removed!", "bookmarkError": "Failed to update bookmark status", "editSuggestionTitle": "Suggest an edit", "editSuggestionChooseSpiciness": "Choose spiciness level", "editSuggestionFieldLabel": "Suggest your edit", "editSuggestionSendButton": "Send suggestion", "editSuggestionSuccess": "Thank you for your suggestion!", "generalError": "Something went wrong. Please try again.", "reportTitle": "Report as inaccurate", "reportReasonLabel": "Select a reason*", "reportDetailLabel": "Additional details", "reportSendButton": "Send report", "reportSuccess": "Your report has been sent!", "reportSentTitle": "Report sent", "reportSentMessage": "Changes will be reflected if approved by the admin", "suggestionSentTitle": "Suggestion sent", "ok": "Ok", "reportReasonIncorrectMeaning": "Incorrect meaning", "reportReasonIncorrectLanguage": "Incorrect language", "reportReasonNotACussword": "This is not a cussword", "reportReasonOther": "Other", "bookmarksTitle": "Bookmarks", "bookmarksEmptyMessage": "You don't have any bookmarked words yet", "dashboard": "Dashboard", "spicinessSelectionRequired": "At least 1 selection is required", "allBookmarks": "All Bookmarks", "languages": "Languages", "words": "Words", "searchTitle": "Search", "searchHint": "Search for words or languages", "searchEmptyResultsTitle": "No results found", "searchEmptyResultsMessage": "Try a different search term", "searchErrorMessage": "Something went wrong. Please try again.", "meaning": "Meaning:", "aboutCussmeTab": "About <PERSON>ussme", "theStoryTab": "The Story", "aboutCussmeTitle": "About <PERSON>ussme", "aboutCussmeAppTitle": "About Cussme app", "privacyPolicy": "Privacy Policy", "termsAndConditions": "Terms & Conditions", "linkSeparator": " | ", "aboutAppDescription1": "Introducing \"Cussme\", the only app where you can insult someone in various regional languages with proper cultural context, pronunciation guides, and bonus sass.", "aboutAppDescription2": "Whether you want to cuss like a Glaswegian dockworker, throw shade like a Brazilian soap opera villain, or master the art of passive-aggressive Korean auntie slang, this app has your back (and your tongue). Say goodbye to generic \"bad words\" and hello to regionally-specific, grandma-disapproved masterpieces of linguistic spice.\n\nUsers can filter through categories like \"Mild,\" \"Cheeky,\" \"Spicy\" and \"Slang\" Each entry comes with meaning, phonetics, and example usage which could get you banned from a bar in Tokyo versus which will earn you a knowing nod in Naples.\n\nThe goal of the app is to foster a sense of learning and awareness of local cultures, slangs, and origins of some of these words. This project was completely self-funded, so consider subscribing for the full version if you can.", "specialThanksTo": "Special thanks to", "theStoryDescription1": "Hi! My name is ", "theStoryDescription2": "<PERSON><PERSON><PERSON>", "theStoryDescription3": " and I am a Product Designer who loves teaching design. I love problem solving, care about innovative design, and find my peace in cooking. I have also worked in Film, TV, advertising and write scripts and screenplays as a hobby.\n\nCussMe was an idea born out of boredom as I was trying to build my UX portfolio way back in 2012. It was a silly idea which was usually a conversation starter and nothing beyond it. Now more than 10 years later I decided to pursue building this app as a side quest.\n\nI am excited to hear your feedback, especially criticism and look forward to your reviews.\n\nSince this project was fully self-funded, it would be great if you try using our subscription package 🧿", "shakibHabibiName": "<PERSON><PERSON><PERSON>", "leadDeveloperRole": "Lead Developer", "mozyMervinName": "<PERSON><PERSON>", "consultantProductOwnerRole": "Consultant Product Owner", "paymentPlansAdFreeExperience": "100% Ad-free experience", "paymentPlansFullWordList": "Access full list of words (more than 400 words and phrases)", "paymentPlansSpeechPlayback": "Access to speech playback", "paymentPlansSpeechPlaybackBeta": "Access to speech playback (beta)", "paymentPlansAnnualPlan": "US$8.99 Annually", "paymentPlansAnnualPlanSubtitle": "(US$0.75/month)", "paymentPlansMonthlyPlan": "US$1.49 Monthly", "paymentPlansSave51": "Save 51%", "paymentPlansContinue": "Continue", "premiumLimitationWarning": "Get access to over 400 words and phrases", "subscribeToPremium": "Subscribe to Premium", "subscribeToPremiumToUnlock": "Subscribe to Premium to unlock", "failedToInitiatePurchase": "Failed to initiate purchase", "purchaseUnavailable": "In-app purchases are not available on this device.", "purchaseSuccess": "Purchase successfully completed", "restoreFailed": "Purchase restoration failed. Please ensure you have an active subscription.", "restoreSuccess": "Purchase restored successfully", "notCompletePurchase": "Purchase not completed. Please restore your purchase.", "restorePurchase": "Rest<PERSON>", "restorePurchaseDescription": "Already have a subscription? Restore your purchase", "signInRequired": "Sign in required", "signInRequiredDescription": "A signed in account is required to complete this action", "restoreYourPurchase": "Restore your purchase", "subscriptionSuccessTitle": "Thank you for subscribing to", "subscriptionSuccessDescription": "Watch out for more languages, fun games (coming soon), and much more!", "close": "Close", "eula": "EULA", "noPurchaseFound": "No purchase found"}