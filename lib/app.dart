import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'localization/generated/l10n.dart';
import 'routing/app_router.dart';
import 'ui/ui.dart';

class CussMeApp extends ConsumerWidget {
  const CussMeApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp.router(
      localizationsDelegates: const [
        Str.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: Str.delegate.supportedLocales,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Palette.primary,
          primary: Palette.primary,
          onPrimary: Palette.onPrimary,
          surface: Palette.surface,
          onSurface: Palette.onSurface,
          surfaceContainerHighest: Palette.surfaceVariant,
          onSurfaceVariant: Palette.onSurfaceVariant,
          error: Palette.error,
        ),
        useMaterial3: true,
        textTheme: const TextTheme(
          displayLarge: TextStyles.displayLarge,
          displayMedium: TextStyles.displayMedium,
          displaySmall: TextStyles.displaySmall,
          headlineLarge: TextStyles.headlineLarge,
          headlineMedium: TextStyles.headlineMedium,
          headlineSmall: TextStyles.headlineSmall,
          titleLarge: TextStyles.titleLarge,
          titleMedium: TextStyles.titleMedium,
          titleSmall: TextStyles.titleSmall,
          bodyLarge: TextStyles.bodyLarge,
          bodyMedium: TextStyles.bodyMedium,
          bodySmall: TextStyles.bodySmall,
          labelLarge: TextStyles.labelLarge,
          labelMedium: TextStyles.labelMedium,
          labelSmall: TextStyles.labelSmall,
        ),
      ),
      title: 'CussMe',
      routerConfig: ref.watch(goRouterProvider),
    );
  }
}
