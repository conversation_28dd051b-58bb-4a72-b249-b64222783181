import '../../localization/generated/l10n.dart';

enum Spiciness {
  mild,
  cheeky,
  spicy,
  slang;

  String getTitle() {
    switch (this) {
      case Spiciness.mild:
        return Str.current.spicinessMildTitle;
      case Spiciness.cheeky:
        return Str.current.spicinessCheekyTitle;
      case Spiciness.spicy:
        return Str.current.spicinessSpicyTitle;
      case Spiciness.slang:
        return Str.current.spicinessSlangTitle;
    }
  }

  String getDescription() {
    switch (this) {
      case Spiciness.mild:
        return Str.current.spicinessMildDescription;
      case Spiciness.cheeky:
        return Str.current.spicinessCheekyDescription;
      case Spiciness.spicy:
        return Str.current.spicinessSpicyDescription;
      case Spiciness.slang:
        return Str.current.spicinessSlangDescription;
    }
  }

  String getIconPath() {
    switch (this) {
      case Spiciness.mild:
        return 'assets/images/ic_mild.svg';
      case Spiciness.cheeky:
        return 'assets/images/ic_cheeky.svg';
      case Spiciness.spicy:
        return 'assets/images/ic_spicy.svg';
      case Spiciness.slang:
        return 'assets/images/ic_slang.svg';
    }
  }

  static Spiciness byName(String? value) {
    try {
      return Spiciness.values.byName(value!);
    } catch (_) {
      return Spiciness.mild;
    }
  }

  static List<Spiciness> fromJsonList(List<dynamic> jsonList) =>
      jsonList.map((item) => Spiciness.byName(item as String)).toList();
}
