import '../../localization/generated/l10n.dart';

enum ReportReason {
  incorrectMeaning,
  incorrectLanguage,
  notACussword,
  other;

  String getTitle() {
    switch (this) {
      case ReportReason.incorrectMeaning:
        return Str.current.reportReasonIncorrectMeaning;
      case ReportReason.incorrectLanguage:
        return Str.current.reportReasonIncorrectLanguage;
      case ReportReason.notACussword:
        return Str.current.reportReasonNotACussword;
      case ReportReason.other:
        return Str.current.reportReasonOther;
    }
  }
}
