import 'package:freezed_annotation/freezed_annotation.dart';

part 'language_entity.freezed.dart';
part 'language_entity.g.dart';

@Freezed(toJson: true)
abstract class LanguageEntity with _$LanguageEntity {
  const factory LanguageEntity({
    required String id,
    required String name,
    required String locale,
  }) = _LanguageEntity;

  factory LanguageEntity.fromJson(Map<String, dynamic> json) =>
      _$LanguageEntityFromJson(json);

  static List<LanguageEntity> fromJsonList(dynamic data) {
    if (data is! List) return [];

    return data
        .whereType<Map<String, dynamic>>()
        .map(LanguageEntity.fromJson)
        .toList();
  }
}
