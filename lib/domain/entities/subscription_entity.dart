import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_entity.freezed.dart';
part 'subscription_entity.g.dart';

@Freezed(toJson: true)
abstract class SubscriptionEntity with _$SubscriptionEntity {
  const factory SubscriptionEntity({
    String? id,
    String? profileId,
    required String productId,
    required String platform,
    required String purchaseToken,
    DateTime? expiresAt,
    String? purchaseId,
    DateTime? createdAt,
  }) = _SubscriptionEntity;

  factory SubscriptionEntity.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionEntityFromJson(json);

  factory SubscriptionEntity.fromSupabase(Map<String, dynamic> data) {
    return SubscriptionEntity(
      id: data['id'],
      profileId: data['profile_id'],
      productId: data['product_id'],
      platform: data['platform'],
      purchaseToken: data['purchase_token'],
      expiresAt: data['expires_at'] != null
          ? DateTime.parse(data['expires_at'])
          : null,
      purchaseId: data['purchase_id'],
      createdAt: data['created_at'] != null
          ? DateTime.parse(data['created_at'])
          : null,
    );
  }
}
