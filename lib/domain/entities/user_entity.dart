import 'package:cussme/data/supabase/supabase_tables.dart';
import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_entity.freezed.dart';
part 'user_entity.g.dart';

@Freezed(toJson: true)
abstract class UserEntity with _$UserEntity {
  const factory UserEntity({
    required String id,
    required String email,
    required String firstName,
    String? lastName,
    required AuthProvider provider,
    required List<Spiciness> spiciness,
    required List<LanguageEntity> languages,
    @Default(false) bool isPremium,
  }) = _UserEntity;

  factory UserEntity.fromJson(Map<String, dynamic> json) =>
      _$UserEntityFromJson(json);

  factory UserEntity.fromSupabase(Map<String, dynamic> response) {
    final columns =
        SupabaseTable.aggregatedProfile.columns as AggregatedProfileColumns;

    return UserEntity(
      id: response[columns.id],
      email: response[columns.email],
      firstName: response[columns.firstName],
      lastName: response[columns.lastName],
      provider: AuthProvider.byName(response[columns.provider]),
      spiciness: Spiciness.fromJsonList(response[columns.spiciness]),
      languages: LanguageEntity.fromJsonList(response[columns.languages]),
      isPremium: response[columns.isPremium] ?? false,
    );
  }
}
