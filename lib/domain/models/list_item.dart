import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'list_item.freezed.dart';

@freezed
sealed class ListItem with _$ListItem {
  const factory ListItem.categoryItem(String header) = ListItemCategory;
  const factory ListItem.headerItem(String header) = ListItemHeader;
  const factory ListItem.wordItem(WordEntity word) = ListItemWord;
  const factory ListItem.languageItem(LanguageEntity language) =
      ListItemLanguage;
  const factory ListItem.adItem(String key) = ListItemAd;
  const factory ListItem.premiumWarningItem(WordEntity word) =
      ListItemPremiumWarning;
  const factory ListItem.blurredWordItem(WordEntity word) = ListItemBlurredWord;
}
