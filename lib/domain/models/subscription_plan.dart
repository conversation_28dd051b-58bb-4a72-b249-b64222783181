import 'package:cussme/localization/generated/l10n.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_plan.freezed.dart';

const annualProductId = 'annual_sub_discount';
const monthlyProductId = 'monthly_sub';

@freezed
abstract class SubscriptionPlan with _$SubscriptionPlan {
  const factory SubscriptionPlan({
    required String productId,
    required String title,
    required double price,
    String? subtitle,
    String? badge,
  }) = _SubscriptionPlan;

  const SubscriptionPlan._();

  bool get isAnnual => productId == annualProductId;
  bool get isMonthly => productId == monthlyProductId;

  static List<SubscriptionPlan> getPlans() => [
        SubscriptionPlan(
          productId: annualProductId,
          title: Str.current.paymentPlansAnnualPlan,
          price: 8.99,
          subtitle: Str.current.paymentPlansAnnualPlanSubtitle,
          badge: Str.current.paymentPlansSave51,
        ),
        SubscriptionPlan(
          productId: monthlyProductId,
          title: Str.current.paymentPlansMonthlyPlan,
          price: 1.49,
        ),
      ];

  static SubscriptionPlan getAnnualPlan() =>
      getPlans().firstWhere((plan) => plan.isAnnual);
  static SubscriptionPlan getMonthlyPlan() =>
      getPlans().firstWhere((plan) => plan.isMonthly);
  static List<String> getProductIds() =>
      getPlans().map((plan) => plan.productId).toList();
}
