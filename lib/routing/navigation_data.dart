import 'package:cussme/domain/domain.dart';
import 'package:cussme/routing/route_constants.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'navigation_data.freezed.dart';

@freezed
abstract class NavigationData with _$NavigationData {
  const NavigationData._();

  const factory NavigationData({
    String? destination,
    String? key,
    LanguageEntity? language,
    UserEntity? user,
  }) = _NavigationData;

  bool get shouldSkipDestination =>
      user?.isPremium == true &&
      destination != null &&
      destination == paymentPlansPath;
}
