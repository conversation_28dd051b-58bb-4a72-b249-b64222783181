import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class BottomSheetPageTransition<T> extends CustomTransitionPage<T> {
  const BottomSheetPageTransition({
    required super.child,
    super.name,
    super.arguments,
    super.restorationId,
    super.key,
  }) : super(
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: _transitionsBuilder,
        );

  static Widget _transitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const begin = Offset(0.0, 1.0);
    const end = Offset.zero;
    const curve = Curves.easeInOut;

    final tween = Tween(begin: begin, end: end);
    final curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: curve,
    );

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(curvedAnimation);

    return FadeTransition(
      opacity: fadeAnimation,
      child: SlideTransition(
        position: tween.animate(curvedAnimation),
        child: child,
      ),
    );
  }
}

extension BottomSheetTransition on Widget {
  BottomSheetPageTransition<void> toBottomSheetPage({
    String? name,
    Object? arguments,
    String? restorationId,
    LocalKey? key,
  }) {
    return BottomSheetPageTransition<void>(
      child: this,
      name: name,
      arguments: arguments,
      restorationId: restorationId,
      key: key,
    );
  }
}
