import 'route_constants.dart';

enum RouteLocation {
  splash(splashPath, splashName),
  signIn(signInPath, signInName),
  signUp(signUpPath, signUpName),
  appleWebView(appleWebViewPath, appleWebViewName),
  forgotPassword(forgotPasswordPath, forgotPasswordName),
  resetPasswordNew(resetPasswordNewPath, resetPasswordNewName),
  resetPasswordSent(resetPasswordSentPath, resetPasswordSentName),
  resetPasswordSuccess(resetPasswordSuccessPath, resetPasswordSuccessName),
  home(homePath, homeName),
  guestHome(guestHomePath, guestHomeName),
  intro(introPath, introName),
  preferences(preferencesPath, preferencesName),
  accountSettings(accountSettingsPath, accountSettingsName),
  subInfo(subInfoPath, subInfoName),
  spiciness(spicinessPath, spicinessName),
  languages(languagesPath, languagesName),
  wordDetail(wordDetailPath, wordDetailName),
  editSuggestion(editSuggestionPath, editSuggestionName),
  report(reportPath, reportName),
  bookmarks(bookmarksPath, bookmarksName),
  wordList(wordListPath, wordListName),
  search(searchPath, searchName),
  about(aboutPath, aboutName),
  paymentPlans(paymentPlansPath, paymentPlansName);

  final String path;
  final String name;
  const RouteLocation(this.path, this.name);
}
