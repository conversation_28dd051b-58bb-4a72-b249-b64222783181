import 'dart:convert';

import 'package:cussme/data/supabase/supabase_tables.dart';
import 'package:cussme/domain/domain.dart';

extension SupabaseExtension on dynamic {
  WordEntity toWordEntity() {
    final columns = SupabaseTable.words.columns as WordColumns;

    List<String> usages = [];
    if (this[columns.usages] != null) {
      if (this[columns.usages] is List) {
        usages = List<String>.from(this[columns.usages]);
      } else if (this[columns.usages] is String) {
        try {
          final dynamic parsedJson = jsonDecode(this[columns.usages] as String);
          if (parsedJson is List) {
            usages = List<String>.from(parsedJson);
          }
        } catch (_) {}
      }
    }

    return WordEntity(
      id: this[columns.id],
      word: this[columns.word],
      language: this[columns.language],
      meaning: this[columns.meaning],
      spiciness: Spiciness.byName(this[columns.spiciness]),
      usages: usages,
      phonetic: this[columns.phonetic],
      createdAt: DateTime.parse(this[columns.createdAt]),
    );
  }
}

extension ListToWordEntityExtension on List {
  List<WordEntity> toWordEntityList() {
    return whereType<Map<String, dynamic>>()
        .map((item) => item.toWordEntity())
        .toList();
  }
}
