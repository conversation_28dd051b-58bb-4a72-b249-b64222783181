// ignore: depend_on_referenced_packages
import 'package:collection/collection.dart';

extension ListExtension<T> on List<T> {
  List<T> copy() {
    return List<T>.from(this);
  }

  List<T> toggleItem(T item) {
    if (contains(item)) {
      return [...this]..remove(item);
    } else {
      return [...this, item];
    }
  }

  bool isEqualTo(List<T> other) =>
      const DeepCollectionEquality.unordered().equals(this, other);
}
