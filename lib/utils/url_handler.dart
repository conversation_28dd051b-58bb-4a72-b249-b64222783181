import 'package:url_launcher/url_launcher.dart';

const String privacyPolicyUrl = 'https://cussme.xyz/privacy-policy/';
const String termsAndConditionsUrl = 'https://cussme.xyz/terms-and-conditions/';
const String eulaUrl = 'https://cussme.xyz/end-user-licence-agreement-eula/';
const String shakibProfileUrl = 'https://shakibhabibi.github.io/';
const String mozyProfileUrl = 'https://mmervin.magnumlynx.com/';
const String abishekProfileUrl = 'https://www.linkedin.com/in/abishekantony/';

void openInBrowser(String targetUrl) async {
  final url = Uri.parse(targetUrl);
  if (await canLaunchUrl(url)) {
    await launchUrl(url, mode: LaunchMode.externalApplication);
  } else {
    throw 'Could not launch $url';
  }
}
