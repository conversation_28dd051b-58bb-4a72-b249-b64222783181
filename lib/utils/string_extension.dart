extension NullableStringExtensions on String? {
  /// Returns 'User' + random 4-digit number if the string is null or empty
  String extractFirstName() {
    if (this == null || this!.isEmpty) {
      final randomNumber = 1000 + DateTime.now().millisecondsSinceEpoch % 9000;
      return 'User$randomNumber';
    }

    final nameParts = this!.split(' ');
    return nameParts.first;
  }

  String? extractLastName() {
    if (this == null || this!.isEmpty) {
      return null;
    }

    final nameParts = this!.split(' ');
    return nameParts.length > 1 ? nameParts.skip(1).join(' ') : null;
  }

  String capitalize() {
    if (this == null || this!.isEmpty) {
      return this ?? '';
    }

    return '${this![0].toUpperCase()}${this!.substring(1)}';
  }

  bool orContains(List<String> values) {
    if (this == null) return false;
    for (final value in values) {
      if (this!.contains(value)) {
        return true;
      }
    }
    return false;
  }

  bool andContains(List<String> values) {
    if (this == null) return false;
    for (final value in values) {
      if (!this!.contains(value)) {
        return false;
      }
    }
    return true;
  }

  String get firstLetter =>
      this == null ? '' : this!.substring(0, 1).toUpperCase();
}

extension StringExtensions on String {
  String get bearer => 'Bearer $this';
  String get firstLetter => substring(0, 1).toUpperCase();
}
