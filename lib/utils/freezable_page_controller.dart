import 'package:flutter/material.dart';

/// A wrapper around PageController that can be used with Freezed
/// This is needed because PageController is not a value type and cannot be directly used in Freezed classes
class FreezablePageController {
  final PageController _controller;
  
  FreezablePageController({
    int initialPage = 0,
    double viewportFraction = 1.0,
  }) : _controller = PageController(
         initialPage: initialPage,
         viewportFraction: viewportFraction,
       );
  
  PageController get controller => _controller;
  
  void dispose() {
    _controller.dispose();
  }
  
  Future<void> animateToPage(
    int page, {
    required Duration duration,
    required Curve curve,
  }) {
    if (_controller.hasClients) {
      return _controller.animateToPage(
        page,
        duration: duration,
        curve: curve,
      );
    }
    return Future.value();
  }
  
  void jumpToPage(int page) {
    if (_controller.hasClients) {
      _controller.jumpToPage(page);
    }
  }
}
