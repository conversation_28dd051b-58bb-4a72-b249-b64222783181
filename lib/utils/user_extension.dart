import 'package:cussme/domain/domain.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

extension UserExtension on User {
  AuthProvider get provider {
    final providerString = appMetadata['provider'] as String?;
    return AuthProvider.byName(providerString!);
  }

  String get fullName {
    if (userMetadata == null || userMetadata!.isEmpty) return '';

    final fullName = userMetadata!['full_name'] as String?;
    if (fullName != null) return fullName;

    final name = userMetadata!['name'] as String?;
    if (name != null) return name;

    return '';
  }
}
