import 'package:cussme/data/supabase/supabase_constants.dart';
import 'package:cussme/domain/domain.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../localization/generated/l10n.dart';

class ExceptionHandler {
  static CussMeException handleError(dynamic error) {
    final defaultMessage = Str.current.somethingWentWrong;

    if (error == null) {
      return CussMeException(defaultMessage);
    } else if (error is AuthException) {
      if (error.code == alreadyExistsErrorCode) {
        return const EmailAlreadyExistsException();
      }
      final message = error.message.isNotEmpty ? error.message : defaultMessage;
      return CussMeException(message);
    } else if (error is CussMeException) {
      return error;
    } else if (error is StorageException) {
      final message = error.message.isNotEmpty
          ? 'Storage error: ${error.message}'
          : defaultMessage;
      return CussMeException(message);
    } else if (error is PostgrestException) {
      final message = error.message.isNotEmpty ? error.message : defaultMessage;
      return CussMeException(message);
    } else if (error is String) {
      return CussMeException(error);
    } else {
      final errorMessage = error.toString();
      // Check if it's not just the generic 'Exception: ' or 'Error: ' format
      if (errorMessage.contains(':') && errorMessage.split(':').length > 1) {
        final actualMessage =
            errorMessage.split(':').sublist(1).join(':').trim();
        return CussMeException(
            actualMessage.isNotEmpty ? actualMessage : defaultMessage);
      }
      return CussMeException(defaultMessage);
    }
  }
}
