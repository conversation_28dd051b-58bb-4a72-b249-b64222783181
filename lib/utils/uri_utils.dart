/// Utility extensions for Uri handling
extension UriExtensions on Uri {
  /// Extracts a parameter value from different parts of the URI
  ///
  /// This method systematically checks for the parameter in multiple locations:
  /// 1. Query parameters
  /// 2. Fragment parameters
  /// 3. Full URI string (using RegExp)
  ///
  /// @param paramName The name of the parameter to extract
  /// @return The parameter value or null if not found
  String? getParameter(String paramName) {
    // First check standard query parameters
    if (queryParameters.containsKey(paramName)) {
      return queryParameters[paramName];
    }

    // Then check fragment parameters if available
    if (fragment.isNotEmpty) {
      final fragmentParams = Uri.splitQueryString(fragment);
      if (fragmentParams.containsKey(paramName)) {
        return fragmentParams[paramName];
      }
    }

    // Finally try regex as fallback (for cases like Supabase redirects)
    final fullUrl = toString();
    final regExp = RegExp('$paramName=([^&]+)');
    final match = regExp.firstMatch(fullUrl);
    if (match != null && match.groupCount >= 1) {
      return Uri.decodeComponent(match.group(1)!);
    }

    return null;
  }
}
