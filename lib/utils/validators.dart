import '../localization/generated/l10n.dart';

class Validators {
  static String? validateEmail(String email) {
    if (email.isEmpty) {
      return Str.current.emailEmptyError;
    }

    // Standard email regex pattern
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');

    if (!emailRegex.hasMatch(email)) {
      return Str.current.emailInvalidError;
    }

    return null;
  }

  static String? validatePassword(String password) {
    if (password.isEmpty) {
      return Str.current.passwordEmptyError;
    }

    if (password.length < 8) {
      return Str.current.passwordTooShortError;
    }

    // Check if password contains at least one number
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      return Str.current.passwordNoNumberError;
    }

    // Check if password contains at least one letter
    if (!RegExp(r'[a-zA-Z]').hasMatch(password)) {
      return Str.current.passwordNotEnoughLettersError;
    }

    return null;
  }

  static String? validateConfirmPassword(
      String password, String confirmPassword) {
    if (confirmPassword.isEmpty) {
      return Str.current.confirmPasswordEmptyError;
    }

    if (confirmPassword != password) {
      return Str.current.passwordsDoNotMatchError;
    }

    return null;
  }
}
