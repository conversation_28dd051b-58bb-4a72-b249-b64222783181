import 'package:cussme/data/supabase/supabase_constants.dart';
import 'package:cussme/utils/utils.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

extension SupabaseRpcExtension on SupabaseClient {
  Future<void> deleteUser() async {
    try {
      await rpc('delete_user');
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  Future<void> updateUserLanguages(
      String userId, List<String> languageIds) async {
    try {
      await rpc(
        'update_user_languages',
        params: {'user_id': userId, 'new_language_ids': languageIds},
      );
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  Future<Map<String, dynamic>> getAggregatedProfile({String? userId}) async {
    try {
      final user = auth.currentUser;
      final profile = await rpc(
        'get_aggregated_profile',
        params: {'uid': userId ?? user?.id ?? defaultUid},
      );

      return profile;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  Future<bool> checkEmailExists(String email) async {
    try {
      final bool response = await rpc(
        'check_email_exists',
        params: {'email_to_check': email},
      );
      return response;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  Future<List<Map<String, dynamic>>> getBookmarkedWords(String userId) async {
    try {
      final response = await rpc(
        'get_bookmarked_words',
        params: {'uid': userId},
      );
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }
}
