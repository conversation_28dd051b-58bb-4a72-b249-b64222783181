enum SupabaseTable {
  profiles(ProfileColumns(), 'profiles'),
  aggregatedProfile(AggregatedProfileColumns(), 'aggregated_profile'),
  languages(LanguageColumns(), 'languages'),
  words(WordColumns(), 'words'),
  bookmarks(BookmarkColumns(), 'bookmarks'),
  wordSuggestions(WordSuggestionColumns(), 'suggestions'),
  wordReports(WordReportColumns(), 'reports'),
  subscriptions(SubscriptionColumns(), 'subscriptions');

  final Columns columns;
  final String name;
  const SupabaseTable(this.columns, this.name);
}

abstract class Columns {
  const Columns();
}

class ProfileColumns extends Columns {
  const ProfileColumns();

  final String id = 'id';
  final String firstName = 'first_name';
  final String lastName = 'last_name';
  final String spiciness = 'spiciness';
  final String isPremium = 'is_premium';
}

class AggregatedProfileColumns extends Columns {
  const AggregatedProfileColumns();

  final String id = 'id';
  final String email = 'email';
  final String firstName = 'first_name';
  final String lastName = 'last_name';
  final String spiciness = 'spiciness';
  final String languages = 'languages';
  final String provider = 'provider';
  final String isPremium = 'is_premium';
}

class LanguageColumns extends Columns {
  const LanguageColumns();

  final String id = 'id';
  final String name = 'name';
}

class WordColumns extends Columns {
  const WordColumns();

  final String id = 'id';
  final String word = 'word';
  final String language = 'language';
  final String meaning = 'meaning';
  final String spiciness = 'spiciness';
  final String usages = 'usages';
  final String phonetic = 'phonetic';
  final String createdAt = 'created_at';
}

class BookmarkColumns extends Columns {
  const BookmarkColumns();

  final String profileId = 'profile_id';
  final String wordId = 'word_id';
}

class WordSuggestionColumns extends Columns {
  const WordSuggestionColumns();

  final String id = 'id';
  final String wordId = 'word_id';
  final String profileId = 'profile_id';
  final String suggestion = 'suggestion';
  final String spiciness = 'spiciness';
  final String createdAt = 'created_at';
}

class WordReportColumns extends Columns {
  const WordReportColumns();

  final String id = 'id';
  final String wordId = 'word_id';
  final String profileId = 'profile_id';
  final String reason = 'reason';
  final String detail = 'detail';
  final String createdAt = 'created_at';
}

class SubscriptionColumns extends Columns {
  const SubscriptionColumns();

  final String id = 'id';
  final String profileId = 'profile_id';
  final String productId = 'product_id';
  final String platform = 'platform';
  final String purchaseToken = 'purchase_token';
  final String expiresAt = 'expires_at';
  final String purchaseId = 'purchase_id';
  final String createdAt = 'created_at';
}
