import 'package:cussme/data/supabase/supabase_constants.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/widgets/misc/toast.dart';
import 'package:cussme/utils/utils.dart';
import 'package:go_router/go_router.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../routing/app_router.dart';

class AuthHttpClient extends http.BaseClient {
  final http.Client _inner = http.Client();

  final SharedPreferences _preferences;

  AuthHttpClient(this._preferences);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final response = await _inner.send(request);

    await _checkUserLoggedOut(response);

    return response;
  }

  Future<void> _checkUserLoggedOut(http.StreamedResponse response) async {
    final statusCode = response.statusCode;

    if (statusCode != 401 && statusCode != 403 && statusCode != 204) {
      return;
    }

    if (statusCode == 204) {
      final requestPath = response.request?.url.path;
      if (requestPath.orContains([deleteUserApi, logoutApi])) {
        if (requestPath!.contains(deleteUserApi)) {
          Toast.show(rootNavigatorKey.currentContext!,
              Str.current.deleteAccountSuccess);
        }
        await _clearAndRedirectToSignIn();
        return;
      }

      try {
        final response = await Supabase.instance.client.auth.getUser();
        if (response.user != null) return;
      } catch (_) {
        return;
      }
    }

    await _clearAndRedirectToSignIn();
    throw CussMeException(Str.current.sessionExpiredError);
  }

  Future<void> _clearAndRedirectToSignIn() async {
    await _preferences.clear();

    final router = GoRouter.of(rootNavigatorKey.currentContext!);
    router.goToSignIn();
  }
}
