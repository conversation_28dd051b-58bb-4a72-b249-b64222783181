import 'package:cussme/data/models/requests/filter_words_request.dart';
import 'package:cussme/data/models/requests/word_report_request.dart';
import 'package:cussme/data/models/requests/word_suggestion_request.dart';
import 'package:cussme/data/models/responses/home_response.dart';
import 'package:cussme/data/models/responses/search_response.dart';
import 'package:cussme/domain/domain.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../di/general_provider.dart';
import 'impl/word_repository_impl.dart';

part 'word_repository.g.dart';

abstract class WordRepository {
  Future<List<WordEntity>> searchWords({
    required String languageId,
    required String query,
    List<Spiciness>? spiciness,
  });

  Future<List<WordEntity>> getWords({
    required String languageId,
    List<Spiciness>? spiciness,
  });

  Future<HomeResponse> getHome({required bool isGuest});

  Future<WordEntity> getWordById(
      {required String wordId, required bool isGuest});

  Future<bool> toggleBookmark({
    required String wordId,
    required String profileId,
    required bool currentStatus,
  });

  Future<bool> isBookmarked({
    required String wordId,
    required String profileId,
  });

  Future<bool> submitWordSuggestion(
      {required WordSuggestionRequest suggestion});

  Future<bool> submitWordReport({required WordReportRequest report});

  Future<SearchResponse> search({required String query});

  Future<List<WordEntity>> filterWords({
    required FilterWordsRequest request,
    required bool isAuthenticated,
  });

  Future<List<WordEntity>> getBookmarks({required String userId});
}

@riverpod
WordRepository wordRepository(Ref ref) {
  final supabaseClient = ref.read(supabaseClientProvider);

  return WordRepositoryImpl(supabaseClient: supabaseClient);
}
