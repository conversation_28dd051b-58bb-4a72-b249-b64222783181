import 'dart:async';
import 'dart:io';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_use_case.g.dart';

class SubscriptionUseCase {
  final SubscriptionRepository _subscriptionRepository;
  final AuthUseCase _authUseCase;
  final StreamController<VerificationResult> _verificationResult =
      StreamController<VerificationResult>();

  StreamSubscription<PurchaseResult>? _purchaseSubscription;
  bool _isVerifying = false;
  bool _isDisposed = false;

  SubscriptionUseCase(this._subscriptionRepository, this._authUseCase) {
    _purchaseSubscription = _subscriptionRepository.purchaseResultStream
        .listen(_handlePurchaseUpdates);
  }

  Stream<VerificationResult> get verificationResultStream =>
      _verificationResult.stream;

  void dispose() {
    if (_isDisposed) return;
    _isDisposed = true;
    _purchaseSubscription?.cancel();
    _verificationResult.close();
    _subscriptionRepository.dispose();
  }

  void _handlePurchaseUpdates(PurchaseResult purchaseResult) {
    if (_isDisposed) return;

    if (purchaseResult is PurchaseSuccess) {
      verifyAndSubmit(purchaseResult.purchases);
    } else if (purchaseResult is PurchaseCancelled) {
      _verificationResult.add(const VerificationResult.cancelled());
    } else if (purchaseResult is PurchaseError) {
      _verificationResult.add(
        VerificationResult.error(message: purchaseResult.message),
      );
    }
  }

  Future<void> verifyAndSubmit(List<SubscriptionEntity> purchases) async {
    if (_isDisposed || _isVerifying) return;

    _isVerifying = true;

    try {
      final currentUser = _authUseCase.getCurrentUser();
      if (currentUser == null) {
        _verificationResult.add(
            VerificationResult.error(message: Str.current.userNotLoggedIn));
        return;
      }

      final subs = purchases
          .map((purchase) => purchase.copyWith(profileId: currentUser.id))
          .toList();

      final request = SubscriptionVerificationRequest(
          profileId: currentUser.id, purchases: subs);

      final response = await _subscriptionRepository.verifyAndSubmit(request);

      if (response == null) {
        _verificationResult.add(const VerificationResult.supabaseFailed());
        return;
      }

      await _authUseCase.updateAndSyncUser();

      _verificationResult.add(VerificationResult.success(response));
    } catch (e) {
      _verificationResult.add(VerificationResult.error(
        message: ExceptionHandler.handleError(e).toString(),
      ));
    } finally {
      _isVerifying = false;
    }
  }

  Future<void> purchaseSubscription(String productId) async {
    final isAvailable =
        await _subscriptionRepository.isInAppPurchaseAvailable();
    if (!isAvailable) {
      throw ExceptionHandler.handleError(Str.current.purchaseUnavailable);
    }

    final currentUser = _authUseCase.getCurrentUser();
    if (currentUser == null) {
      throw ExceptionHandler.handleError(Str.current.userNotLoggedIn);
    }

    final productDetail =
        await _subscriptionRepository.getProductDetail(productId);

    final params = Platform.isIOS
        ? Sk2PurchaseParam(
            productDetails: productDetail, applicationUserName: currentUser.id)
        : GooglePlayPurchaseParam(
            productDetails: productDetail, applicationUserName: currentUser.id);

    await _subscriptionRepository.purchaseProduct(params);
  }

  Future<void> restorePurchases() async {
    final currentUser = _authUseCase.getCurrentUser();
    if (currentUser == null) {
      throw ExceptionHandler.handleError(Str.current.userNotLoggedIn);
    }

    await _subscriptionRepository.restorePurchases(currentUser.id);
  }
}

@riverpod
SubscriptionUseCase subscriptionUseCase(Ref ref) {
  final subscriptionRepository = ref.read(subscriptionRepositoryProvider);
  final authUseCase = ref.read(authUseCaseProvider);
  return SubscriptionUseCase(subscriptionRepository, authUseCase);
}

@riverpod
Stream<VerificationResult> verificationResultStream(Ref ref) {
  return ref.read(subscriptionUseCaseProvider).verificationResultStream;
}
