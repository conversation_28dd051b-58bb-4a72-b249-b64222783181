import 'package:cussme/domain/domain.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../localization/generated/l10n.dart';
import '../repositories/auth_repository.dart';

part 'auth_use_case.g.dart';

class AuthUseCase {
  final AuthRepository _authRepository;

  AuthUseCase(this._authRepository);

  Future<UserEntity> signInWithEmail({
    required String email,
    required String password,
  }) async {
    await _authRepository.signInWithEmailPassword(
      email: email,
      password: password,
    );

    return await updateAndSyncUser();
  }

  Future<UserEntity> signUpWithEmail({
    required String email,
    required String password,
    required String firstname,
    String? lastname,
  }) async {
    final supabaseUser = await _authRepository.signUpWithEmailPassword(
      email: email,
      password: password,
    );

    await _authRepository.upsertProfile(
      user: supabaseUser,
      firstName: firstname,
      lastName: lastname,
    );

    return await updateAndSyncUser();
  }

  Future<(UserEntity, bool)> signInWithGoogle() async {
    final googleSignInAccount = await _authRepository.getGoogleSignInAccount();
    await _authRepository.signUpWithGoogle(googleSignInAccount);

    final profileExists = await _authRepository.checkIfProfileExists();
    if (!profileExists) await _authRepository.insertCurrentUserProfile();

    final user = await updateAndSyncUser();
    return (user, profileExists);
  }

  Future<(UserEntity, bool)> signInWithApple() async {
    final appleCredential = await _authRepository.getAppleCredential();
    await _authRepository.signUpWithApple(appleCredential);

    final profileExists = await _authRepository.checkIfProfileExists();
    if (!profileExists) await _authRepository.insertCurrentUserProfile();

    final user = await updateAndSyncUser();
    return (user, profileExists);
  }

  Future<(UserEntity, bool)> signInWithFacebook() async {
    final fbAccessToken = await _authRepository.getFacebookAccessToken();
    await _authRepository.signUpWithFacebook(fbAccessToken);

    final profileExists = await _authRepository.checkIfProfileExists();
    if (!profileExists) await _authRepository.insertCurrentUserProfile();

    final user = await updateAndSyncUser();
    return (user, profileExists);
  }

  Future<void> sendPasswordResetEmail(String? email) async {
    String? emailToUse = email;

    if (emailToUse == null) {
      final currentUser = _authRepository.getUserFromSharedPreferences();
      emailToUse = currentUser?.email;

      if (emailToUse == null) {
        throw ExceptionHandler.handleError(Str.current.userNotLoggedIn);
      }
    }

    final emailExists = await _authRepository.checkIfEmailExists(emailToUse);

    if (!emailExists) {
      throw ExceptionHandler.handleError(Str.current.emailNotFound);
    }

    await _authRepository.sendPasswordResetEmail(emailToUse);
  }

  Future<void> updatePasswordAndSignOut(
    String newPassword,
    String? token,
  ) async {
    if (token != null) {
      await _authRepository.verifyRecoveryOTP(token);
    }

    _checkIfUserCanResetPassword();

    await _authRepository.updatePassword(newPassword);
    await _clearAndSignOut();
  }

  void _checkIfUserCanResetPassword() {
    final supabaseUser = _authRepository.getCurrentSupabaseUser();

    if (supabaseUser == null) {
      throw ExceptionHandler.handleError(Str.current.somethingWentWrong);
    }

    final provider = supabaseUser.provider;
    if (!provider.isEmail) {
      throw ExceptionHandler.handleError(
          Str.current.providerPasswordResetError);
    }
  }

  bool canUserResetPassword() {
    try {
      _checkIfUserCanResetPassword();
      return true;
    } on CussMeException catch (_) {
      return false;
    }
  }

  Future<UserEntity> updateProfile(
    String firstName,
    String? lastName,
  ) async {
    await _authRepository.updateProfile(firstName, lastName);

    return await updateAndSyncUser();
  }

  Future<UserEntity> updateSpiciness(List<Spiciness> spiciness) async {
    await _authRepository.updateSpiciness(spiciness);

    return await updateAndSyncUser();
  }

  Future<UserEntity> updateLanguages(List<LanguageEntity> languages) async {
    await _authRepository.updateLanguages(languages);

    return await updateAndSyncUser();
  }

  Future<List<LanguageEntity>> getAllLanguages() async {
    return await _authRepository.getAllLanguages();
  }

  Future<void> setGuest() async {
    await _authRepository.clearSharedPreferences();
    await _authRepository.setGuest();
  }

  bool isGuest() => _authRepository.isGuest();

  Future<bool> shouldNavigateHome() async {
    final isGuest = _authRepository.isGuest();
    final currentUser = _authRepository.getUserFromSharedPreferences();

    return currentUser != null || isGuest;
  }

  Future<void> signOut() async {
    await _clearAndSignOut();
  }

  Future<void> deleteUser() async {
    await _authRepository.deleteCurrentUser();
    await _clearAndSignOut();
  }

  Future<UserEntity> getUpdatedUser() async => updateAndSyncUser();

  UserEntity? getCurrentUser() =>
      _authRepository.getUserFromSharedPreferences();

  bool isPremium() => getCurrentUser()?.isPremium ?? false;

  Future<void> signOutBasedOnProvider() async {
    final provider = _authRepository.getCurrentSupabaseUser()!.provider;

    await _authRepository.signOutSupabase();

    if (provider.isGoogle) {
      await _authRepository.signOutGoogle();
    } else if (provider.isFacebook) {
      await _authRepository.signOutFacebook();
    }
  }

  Future<void> _clearAndSignOut() async {
    await signOutBasedOnProvider();
    await _authRepository.clearSharedPreferences();
  }

  Future<UserEntity> updateAndSyncUser({UserEntity? userEntity}) async {
    userEntity ??= await _authRepository.getProfile();

    await _authRepository.saveUserToSharedPreferences(userEntity);
    await _authRepository.removeGuest();
    await _authRepository.syncCurrentUserToSupabase();

    return userEntity;
  }
}

@riverpod
AuthUseCase authUseCase(Ref ref) {
  final authRepository = ref.read(authRepositoryProvider);
  return AuthUseCase(authRepository);
}
