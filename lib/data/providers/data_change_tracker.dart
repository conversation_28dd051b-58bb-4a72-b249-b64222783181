import 'package:flutter_riverpod/flutter_riverpod.dart';

enum TrackingFeatures { languages, bookmark }

class DataChangeTracker {
  final Map<String, DateTime> _updateMap = {};
  final Map<String, DateTime> _fetchMap = {};

  void recordUpdate(TrackingFeatures featureKey) {
    _updateMap[featureKey.name] = DateTime.now();
  }

  void recordFetch(String instanceId) {
    _fetchMap[instanceId] = DateTime.now();
  }

  bool shouldRefresh(String instanceId, TrackingFeatures featureKey) {
    final updateTime = _updateMap[featureKey.name];
    final fetchTime = _fetchMap[instanceId];

    if (updateTime == null || fetchTime == null) return false;
    return updateTime.isAfter(fetchTime);
  }

  void clear() {
    _updateMap.clear();
    _fetchMap.clear();
  }
}

final dataChangeTrackerProvider = Provider<DataChangeTracker>((ref) {
  return DataChangeTracker();
});
