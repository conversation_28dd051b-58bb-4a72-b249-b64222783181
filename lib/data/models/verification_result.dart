import 'package:cussme/data/data.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'verification_result.freezed.dart';

@freezed
abstract class VerificationResult with _$VerificationResult {
  const factory VerificationResult.success(
      SubscriptionVerificationResponse response) = VerificationSuccess;
  const factory VerificationResult.error({required String message}) =
      VerificationError;
  const factory VerificationResult.cancelled() = VerificationCancelled;
  const factory VerificationResult.supabaseFailed() =
      VerificationSupabaseFailed;
}
