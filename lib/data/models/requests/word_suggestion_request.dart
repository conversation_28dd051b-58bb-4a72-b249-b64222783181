import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_suggestion_request.freezed.dart';
part 'word_suggestion_request.g.dart';

@Freezed(toJson: true)
abstract class WordSuggestionRequest with _$WordSuggestionRequest {
  const factory WordSuggestionRequest({
    required String wordId,
    required String suggestion,
    required Spiciness spiciness,
    @Json<PERSON>ey(name: 'profile_id') String? profileId,
  }) = _WordSuggestionRequest;

  factory WordSuggestionRequest.fromJson(Map<String, dynamic> json) =>
      _$WordSuggestionRequestFromJson(json);
}
