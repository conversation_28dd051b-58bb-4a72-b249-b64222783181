import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_verification_request.freezed.dart';
part 'subscription_verification_request.g.dart';

@Freezed(toJson: true)
abstract class SubscriptionVerificationRequest with _$SubscriptionVerificationRequest {
  const factory SubscriptionVerificationRequest({
    required String profileId,
    required List<SubscriptionEntity> purchases,
  }) = _SubscriptionVerificationRequest;

  factory SubscriptionVerificationRequest.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionVerificationRequestFromJson(json);
}
