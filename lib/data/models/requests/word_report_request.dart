import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_report_request.freezed.dart';
part 'word_report_request.g.dart';

@Freezed(toJson: true)
abstract class WordReportRequest with _$WordReportRequest {
  const factory WordReportRequest({
    required String wordId,
    required String reason,
    String? detail,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'profile_id') String? profileId,
  }) = _WordReportRequest;

  factory WordReportRequest.fromJson(Map<String, dynamic> json) =>
      _$WordReportRequestFromJson(json);
}
