import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'purchase_result.freezed.dart';

@freezed
abstract class PurchaseResult with _$PurchaseResult {
  const factory PurchaseResult.success({
    required List<SubscriptionEntity> purchases,
  }) = PurchaseSuccess;
  const factory PurchaseResult.error({required String message}) = PurchaseError;
  const factory PurchaseResult.cancelled() = PurchaseCancelled;
}
