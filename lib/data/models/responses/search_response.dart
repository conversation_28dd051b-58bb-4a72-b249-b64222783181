import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/domain.dart';

part 'search_response.freezed.dart';
part 'search_response.g.dart';

@Freezed(toJson: true)
abstract class SearchResponse with _$SearchResponse {
  const factory SearchResponse({
    required List<WordEntity> words,
    required List<LanguageEntity> languages,
  }) = _SearchResponse;

  factory SearchResponse.fromJson(Map<String, dynamic> json) {
    final wordsJson = json['words'] as List<dynamic>?;
    final languagesJson = json['languages'] as List<dynamic>?;

    return SearchResponse(
      words: wordsJson != null ? WordEntity.fromJsonList(wordsJson) : [],
      languages: languagesJson != null
          ? LanguageEntity.fromJsonList(languagesJson)
          : [],
    );
  }
}
