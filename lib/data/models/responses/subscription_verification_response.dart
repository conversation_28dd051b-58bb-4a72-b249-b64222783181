import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_verification_response.freezed.dart';
part 'subscription_verification_response.g.dart';

@Freezed(toJson: true)
abstract class SubscriptionVerificationResponse with _$SubscriptionVerificationResponse {
  const factory SubscriptionVerificationResponse({
    @JsonKey(name: 'is_user_premium') required bool isUserPremium,
  }) = _SubscriptionVerificationResponse;

  factory SubscriptionVerificationResponse.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionVerificationResponseFromJson(json);
}
