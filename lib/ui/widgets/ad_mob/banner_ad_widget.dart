import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:cussme/di/general_provider.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class BannerAdWidget extends ConsumerStatefulWidget {
  final AdSize adSize;
  final double? width;
  final double? height;

  const BannerAdWidget({
    super.key,
    this.adSize = AdSize.banner,
    this.width,
    this.height,
  });

  @override
  ConsumerState<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends ConsumerState<BannerAdWidget>
    with AutomaticKeepAliveClientMixin {
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      await _maybeRequestTrackingPermission(context);
      if (!mounted) return;
      _loadAd();
    });
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  Future<void> _maybeRequestTrackingPermission(BuildContext context) async {
    if (!Platform.isIOS) return;
  
    final status = await AppTrackingTransparency.trackingAuthorizationStatus;

    if (status == TrackingStatus.notDetermined) {
      final accepted = await TrackingPermissionDialog.show(context);
      if (accepted == true) {
        await AppTrackingTransparency.requestTrackingAuthorization();
      }
    }
  }

  void _loadAd() async {
    try {
      final adRequest = await ref.read(adRequestProvider.future);

      if (!mounted) return;

      _bannerAd = BannerAd(
        adUnitId: ref.read(adUnitIdProvider),
        size: widget.adSize,
        request: adRequest,
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            if (mounted) {
              setState(() {
                _isAdLoaded = true;
              });
            }
          },
          onAdFailedToLoad: (ad, error) {
            debugPrint('Banner ad failed to load: ${error.message}');
            ad.dispose();
            if (mounted) {
              setState(() {
                _isAdLoaded = false;
              });
            }
          },
        ),
      );

      _bannerAd?.load();
    } catch (e) {
      debugPrint('Error loading ad request: $e');
      if (mounted) {
        setState(() {
          _isAdLoaded = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (_bannerAd == null || !_isAdLoaded) {
      return SizedBox(
        width: widget.width ?? widget.adSize.width.toDouble(),
        height: widget.height ?? widget.adSize.height.toDouble(),
      );
    }

    return SizedBox(
      width: widget.width ?? _bannerAd?.size.width.toDouble(),
      height: widget.height ?? _bannerAd?.size.height.toDouble(),
      child: AdWidget(ad: _bannerAd!),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
