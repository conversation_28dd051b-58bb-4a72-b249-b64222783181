import 'package:cussme/data/data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdMobScaffold extends ConsumerWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? bottomNavigationBar;
  final FloatingActionButton? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Color? backgroundColor;
  final bool? resizeToAvoidBottomInset;
  final EdgeInsets? padding;
  final AdSize adSize;
  final bool hasBottomSheetBackground;
  final bool hasUserLoaded;

  const AdMobScaffold({
    super.key,
    required this.body,
    this.appBar,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.backgroundColor = Palette.surface,
    this.resizeToAvoidBottomInset,
    this.padding,
    this.adSize = AdSize.banner,
    this.hasBottomSheetBackground = false,
    this.hasUserLoaded = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPremium = ref.read(authUseCaseProvider).isPremium();

    return Scaffold(
      appBar: appBar,
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      body: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            color: hasBottomSheetBackground ? Palette.whiteFa : backgroundColor,
            borderRadius: hasBottomSheetBackground
                ? const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  )
                : null,
          ),
          child: Column(
            children: [
              Expanded(child: body),
              if (!isPremium && hasUserLoaded)
                Container(
                  alignment: Alignment.center,
                  color: Palette.secondaryLight,
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      //320 for mobile and 728 for tablet
                      maxWidth:
                          MediaQuery.of(context).size.width > 600 ? 728 : 320,
                      //90 for mobile and 250 for tablet
                      maxHeight:
                          MediaQuery.of(context).size.width > 600 ? 90 : 50,
                    ),
                    child: AspectRatio(
                      aspectRatio: 6.4,
                      child: BannerAdWidget(adSize: adSize),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
    );
  }
}
