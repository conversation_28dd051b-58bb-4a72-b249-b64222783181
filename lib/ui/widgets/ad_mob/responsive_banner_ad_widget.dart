import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'banner_ad_widget.dart';

class ResponsiveBannerAdWidget extends StatelessWidget {
  final EdgeInsets padding;
  final double designWidth;
  final double designHeight;

  const ResponsiveBannerAdWidget({
    super.key,
    this.padding = EdgeInsets.zero,
    this.designWidth = 336.0,
    this.designHeight = 280.0,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - padding.horizontal;

    final aspectRatio = designWidth / designHeight;

    double adWidth = availableWidth;
    double adHeight = adWidth / aspectRatio;

    if (adWidth > designWidth) {
      adWidth = designWidth;
      adHeight = designHeight;
    }

    const minWidth = 280.0;
    const minHeight = 200.0;

    if (adWidth < minWidth) {
      adWidth = minWidth;
      adHeight = adWidth / aspectRatio;
    }

    if (adHeight < minHeight) {
      adHeight = minHeight;
      adWidth = adHeight * aspectRatio;
    }
    AdSize adSize;
    if (adWidth >= 320 && adHeight >= 250) {
      adSize = AdSize.mediumRectangle;
    } else if (adWidth >= 320 && adHeight >= 100) {
      adSize = AdSize.largeBanner;
    } else {
      adSize = AdSize.banner;
    }

    return Padding(
      padding: padding,
      child: BannerAdWidget(
        key: key,
        adSize: adSize,
        width: adSize.width.toDouble(),
        height: adSize.height.toDouble(),
      ),
    );
  }
}
