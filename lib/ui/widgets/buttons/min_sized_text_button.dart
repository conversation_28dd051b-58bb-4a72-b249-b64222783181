import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class MinSizedTextButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Size minSize;
  final bool isLoading;
  final bool isEnabled;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;

  const MinSizedTextButton({
    super.key,
    required this.text,
    required this.onPressed,
    required this.minSize,
    this.isLoading = false,
    this.isEnabled = true,
    this.backgroundColor = Palette.primary,
    this.foregroundColor = Colors.white,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final bool buttonEnabled = isEnabled && !isLoading;

    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        minimumSize: minSize,
        padding: padding,
      ),
      onPressed: isLoading ? () {} : (isEnabled ? onPressed : null),
      child: isLoading
          ? SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: foregroundColor,
              ),
            )
          : Text(
              text,
              style: TextStyles.labelLarge.copyWith(
                color: buttonEnabled
                    ? foregroundColor
                    : Palette.black1d.withValues(alpha: 0.4),
              ),
            ),
    );
  }
}
