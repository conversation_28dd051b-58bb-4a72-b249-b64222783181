import 'package:flutter/material.dart';

import '../../../localization/generated/l10n.dart';
import '../../styles/palette.dart';
import '../../styles/text_styles.dart';

class ApplyButton extends StatelessWidget {
  const ApplyButton({
    super.key,
    required this.onPressed,
    this.isEnabled = true,
    this.isLoading = false,
    this.padding = const EdgeInsets.fromLTRB(16, 16, 16, 48),
    this.text,
  });

  final VoidCallback? onPressed;
  final bool isEnabled;
  final bool isLoading;
  final EdgeInsetsGeometry padding;
  final String? text;

  @override
  Widget build(BuildContext context) {
    final bool isButtonEnabled = isEnabled && !isLoading;

    return Padding(
      padding: padding,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: !isEnabled && !isLoading
              ? Palette.disabledButtonBackground
              : Palette.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
          ),
          padding: const EdgeInsets.symmetric(vertical: 10),
          minimumSize: const Size(double.infinity, 40),
        ),
        onPressed: isLoading ? () {} : (isEnabled ? onPressed : null),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : Text(
                text ?? Str.of(context).applyChanges,
                style: TextStyles.labelLarge.copyWith(
                  color: isButtonEnabled
                      ? Colors.white
                      : Palette.black1d.withValues(alpha: 0.4),
                ),
              ),
      ),
    );
  }
}
