import 'package:cussme/ui/styles/palette.dart';
import 'package:cussme/ui/styles/text_styles.dart';
import 'package:flutter/material.dart';

class PrimaryTextButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool hasBorder;
  final bool isLoading;
  final bool isEnabled;
  final Color? textColor;
  final Color? foregroundColor;
  final Color? disabledColor;
  final EdgeInsetsGeometry? padding;
  final bool isShrinkWrap;
  final bool clickableOnDisabled;
  const PrimaryTextButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.hasBorder = false,
    this.isLoading = false,
    this.isShrinkWrap = false,
    this.isEnabled = true,
    this.clickableOnDisabled = true,
    this.textColor = Palette.primary,
    this.foregroundColor = Palette.primary,
    this.disabledColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: isLoading || !isEnabled
          ? (clickableOnDisabled ? () {} : null)
          : onPressed,
      style: TextButton.styleFrom(
        foregroundColor: foregroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(100),
          side: hasBorder
              ? const BorderSide(color: Palette.onSurfaceVariant, width: 1)
              : BorderSide.none,
        ),
        padding: padding,
        tapTargetSize: isShrinkWrap ? MaterialTapTargetSize.shrinkWrap : null,
        minimumSize: isShrinkWrap ? Size.zero : null,
      ),
      child: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                color: Palette.primary,
                strokeWidth: 2,
              ),
            )
          : Text(
              text,
              style: TextStyles.labelLarge.copyWith(
                color: !isEnabled && disabledColor != null
                    ? disabledColor
                    : textColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
    );
  }
}
