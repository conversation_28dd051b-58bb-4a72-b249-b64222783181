import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class WordListCardItem extends StatelessWidget {
  final WordEntity word;
  final VoidCallback? onTap;
  final VoidCallback? onBookmarkTap;
  final VoidCallback? onPlayPronunciation;
  final bool showBookmark;

  const WordListCardItem({
    super.key,
    required this.word,
    required this.onTap,
    required this.onPlayPronunciation,
    this.onBookmarkTap,
    this.showBookmark = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: const BorderSide(
          color: Palette.borderLight,
          width: 1,
        ),
      ),
      color: Palette.surface,
      elevation: 0,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        word.spiciness.getIconPath(),
                        width: 12,
                        height: 12,
                        colorFilter: const ColorFilter.mode(
                          Palette.onSurfaceVariant,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        word.spiciness.getTitle().toUpperCase(),
                        style: TextStyles.labelSmall.copyWith(
                          color: Palette.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Word title
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    word.word,
                                    style: TextStyles.titleMedium.copyWith(
                                      color: Palette.black1d,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                const SizedBox(width: 32),
                              ],
                            ),
                            if (word.phonetic != null) ...[
                              const SizedBox(height: 2),
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  InkWell(
                                    onTap: onPlayPronunciation,
                                    borderRadius: BorderRadius.circular(4),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 2),
                                      child: LayoutBuilder(
                                        builder: (context, constraints) {
                                          return Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              ConstrainedBox(
                                                constraints: BoxConstraints(
                                                  maxWidth:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width -
                                                          58,
                                                ),
                                                child: Text(
                                                  word.phonetic!,
                                                  style: TextStyles.bodyMedium
                                                      .copyWith(
                                                    color: Palette
                                                        .onSurfaceVariant,
                                                    fontStyle: FontStyle.italic,
                                                    fontSize: 14,
                                                  ),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                              const Icon(
                                                Icons.volume_up_outlined,
                                                size: 16,
                                                color: Palette.purpule4f,
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Text(
                        '${Str.of(context).wordDetailMeaning}:',
                        style: TextStyles.bodyMedium.copyWith(
                          color: Palette.black1d,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          word.meaning,
                          style: TextStyles.bodyLarge.copyWith(
                            color: Palette.onSurface,
                            fontSize: 16,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            if (showBookmark && onBookmarkTap != null)
              Positioned(
                top: 22,
                right: 8,
                child: Material(
                  color: Colors.transparent,
                  child: SizedBox(
                    width: 40,
                    height: 40,
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      icon: Icon(
                        word.isBookmarked
                            ? Icons.bookmark
                            : Icons.bookmark_border,
                        size: 24,
                      ),
                      color: word.isBookmarked
                          ? Palette.primary
                          : Palette.onSurfaceVariant,
                      onPressed: onBookmarkTap,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
