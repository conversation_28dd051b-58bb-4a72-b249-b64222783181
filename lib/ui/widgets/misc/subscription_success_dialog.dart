import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

class SubscriptionSuccessDialog extends StatelessWidget {
  const SubscriptionSuccessDialog({super.key});

  static Future<void> show(BuildContext context) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              _navigateToHome(dialogContext);
            }
          },
          child: Dialog(
            backgroundColor: Palette.surface,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(28),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 16),
                      Image.asset(
                        'assets/images/thanks_for_sub.png',
                        width: 72,
                        height: 97,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        Str.of(dialogContext).subscriptionSuccessTitle,
                        style: TextStyles.headlineSmall.copyWith(
                          color: Palette.primary,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: SvgPicture.asset(
                          'assets/images/premium.svg',
                          width: 164,
                          height: 35,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        Str.of(dialogContext).subscriptionSuccessDescription,
                        style: TextStyles.bodyMedium.copyWith(
                          color: Palette.onSurface,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(8, 24, 24, 24),
                    child: PrimaryTextButton(
                      text: Str.of(dialogContext).close,
                      onPressed: () => _navigateToHome(dialogContext),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 10,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static void _navigateToHome(BuildContext context) {
    Navigator.of(context).pop();
    if (context.mounted) {
      GoRouter.of(context).goToHome();
    }
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }
}
