import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class TrackingPermissionDialog extends StatelessWidget {
  const TrackingPermissionDialog({
    super.key,
  });

  static Future<bool?> show(BuildContext context) async {
    return await showDialog<bool?>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return const TrackingPermissionDialog();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final str = Str.of(context);

    return Dialog(
      backgroundColor: Palette.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(28),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  str.trackingPermissionTitle,
                  style: TextStyles.headlineMedium.copyWith(
                    color: Palette.black1d,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  str.trackingPermissionMessage,
                  style: TextStyles.bodyMedium.copyWith(
                    color: Palette.onSurface,
                    letterSpacing: 0.25,
                  ),
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(8, 24, 24, 24),
              child: PrimaryTextButton(
                text: str.continueButton,
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 10,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
