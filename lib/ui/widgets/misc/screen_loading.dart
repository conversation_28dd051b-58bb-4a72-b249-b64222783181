import 'package:flutter/material.dart';
import '../../../localization/generated/l10n.dart';
import '../../styles/palette.dart';
import '../../styles/text_styles.dart';
import '../buttons/primary_button.dart';

enum ScreenLoadingState { loading, error, loaded }

class ScreenLoading extends StatelessWidget {
  final ScreenLoadingState state;
  final String? errorTitle;
  final String? errorDescription;
  final String? buttonText;
  final VoidCallback? onRetry;
  final bool hasBottomSheetBackground;
  final Color backgroungColor;

  const ScreenLoading({
    super.key,
    this.state = ScreenLoadingState.loading,
    this.errorTitle,
    this.errorDescription,
    this.buttonText,
    this.onRetry,
    this.hasBottomSheetBackground = false,
    this.backgroungColor = Palette.surface,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: hasBottomSheetBackground ? Palette.whiteFa : backgroungColor,
          borderRadius: hasBottomSheetBackground
              ? const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                )
              : null,
        ),
        child: Center(
          child: state == ScreenLoadingState.loading
              ? _buildLoadingState()
              : _buildErrorState(),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const SizedBox(
      height: 36,
      width: 36,
      child: CircularProgressIndicator(
        color: Palette.primary,
        strokeWidth: 3,
      ),
    );
  }

  Widget _buildErrorState() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            errorTitle ?? Str.current.screenLoadingErrorTitle,
            textAlign: TextAlign.left,
            style: TextStyles.headlineSmall.copyWith(
              color: Palette.primary,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            errorDescription ?? Str.current.screenLoadingErrorDescription,
            textAlign: TextAlign.left,
            style: TextStyles.bodyMedium.copyWith(
              color: Palette.secondary,
            ),
          ),
          const SizedBox(height: 36.0),
          PrimaryButton(
            text: buttonText ?? Str.current.screenLoadingErrorButtonText,
            onPressed: onRetry ?? () {},
          ),
        ],
      ),
    );
  }
}
