import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class SuccessDialog extends StatelessWidget {
  const SuccessDialog({super.key});

  static Future<void> show(BuildContext context, String title) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Palette.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyles.headlineMedium.copyWith(
                        color: Palette.black1d,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      Str.of(dialogContext).reportSentMessage,
                      style: TextStyles.bodyMedium.copyWith(
                        color: Palette.onSurface,
                        letterSpacing: 0.25,
                      ),
                    ),
                  ],
                ),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(8, 24, 24, 24),
                  child: MinSizedTextButton(
                    text: Str.of(dialogContext).ok,
                    onPressed: () {
                      Navigator.of(dialogContext).pop();
                    },
                    minSize: const Size(80, 40),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 10),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    // Navigate back to previous screen after dialog is closed
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Implementation moved to static show method
    return const SizedBox.shrink();
  }
}
