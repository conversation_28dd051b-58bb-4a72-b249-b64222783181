import 'package:flutter/material.dart';

class Text<PERSON>ithHighlight extends StatelessWidget {
  final String text;
  final String highlightedText;
  final TextStyle style;
  final TextStyle highlightStyle;

  const TextWithHighlight({
    super.key,
    required this.text,
    required this.highlightedText,
    required this.style,
    required this.highlightStyle,
  });

  @override
  Widget build(BuildContext context) {
    if (!text.contains(highlightedText)) {
      return Text(text, style: style);
    }

    final parts = text.split(highlightedText);
    final textSpans = <TextSpan>[];

    for (int i = 0; i < parts.length; i++) {
      textSpans.add(TextSpan(text: parts[i]));
      if (i < parts.length - 1) {
        textSpans.add(TextSpan(
          text: highlightedText,
          style: highlightStyle,
        ));
      }
    }

    return RichText(
      text: TextSpan(
        style: style,
        children: textSpans,
      ),
    );
  }
}
