import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class CussMeBackButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color? color;
  final double? iconSize;
  final TextStyle? textStyle;

  const CussMeBackButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.color,
    this.iconSize,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(20),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.keyboard_arrow_left,
            color: color ?? Palette.primary,
            size: iconSize ?? 24,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: textStyle ??
                TextStyles.labelLarge.copyWith(color: color ?? Palette.primary),
          ),
        ],
      ),
    );
  }
}
