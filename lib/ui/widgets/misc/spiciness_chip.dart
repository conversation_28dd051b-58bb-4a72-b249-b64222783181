import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SpicinessChip extends StatelessWidget {
  final Spiciness spiciness;
  final bool isSelected;
  final ValueChanged<Spiciness> onSelected;

  const SpicinessChip({
    super.key,
    required this.spiciness,
    required this.isSelected,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: InkWell(
        onTap: () => onSelected(spiciness),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isSelected ? Palette.whiteD3 : Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? Palette.secondary : Palette.outlineVariant,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isSelected)
                const Icon(Icons.check, size: 16, color: Palette.secondary),
              if (!isSelected)
                SvgPicture.asset(
                  spiciness.getIconPath(),
                  width: 18,
                  height: 18,
                  colorFilter:
                      const ColorFilter.mode(Palette.primary, BlendMode.srcIn),
                ),
              const SizedBox(width: 4),
              Text(
                spiciness.getTitle(),
                style: TextStyles.bodyMedium.copyWith(
                  color: isSelected ? Palette.secondary : Palette.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
