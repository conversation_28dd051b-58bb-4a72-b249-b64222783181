import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class PremiumWarningWidget extends StatelessWidget {
  final VoidCallback onSubscribeTap;
  final WordEntity? word;

  const PremiumWarningWidget({
    super.key,
    this.word,
    required this.onSubscribeTap,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: word != null
          ? Stack(
              children: [
                WordListCardItem(
                  word: word!,
                  onTap: null,
                  onBookmarkTap: null,
                  onPlayPronunciation: null,
                ),
                Positioned.fill(child: _buildPremiumWarning(context, false)),
              ],
            )
          : _buildPremiumWarning(context, true),
    );
  }

  Widget _buildPremiumWarning(BuildContext context, bool stickToBottom) {
    return ClipRect(
      child: Container(
        padding: EdgeInsets.fromLTRB(16, stickToBottom ? 47 : 0, 16, 12),
        margin: const EdgeInsets.only(top: 6),
        decoration: BoxDecoration(
          gradient: stickToBottom
              ? LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0.0, 0.285, 0.61],
                  colors: [
                    Palette.yellowD3.withValues(alpha: 0),
                    Palette.yellowD3.withValues(alpha: 0.8),
                    Palette.yellowD3,
                  ],
                )
              : null,
          color: stickToBottom ? null : Palette.yellowD3.withValues(alpha: 0.9),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              Str.of(context).premiumLimitationWarning,
              style: TextStyles.titleMedium.copyWith(color: Palette.primary),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            PrimaryButton(
              text: Str.of(context).subscribeToPremium,
              onPressed: onSubscribeTap,
            ),
          ],
        ),
      ),
    );
  }
}
