import 'package:cussme/data/data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../../routing/app_router.dart';

class CussMeHeader extends ConsumerWidget {
  final String title;
  final IconData titleIcon;
  final Function() onBackPressed;
  final Function()? onLogoPressed;
  final String backButtonText;
  final bool isLargeTitle;

  const CussMeHeader({
    super.key,
    required this.title,
    required this.titleIcon,
    required this.backButtonText,
    required this.onBackPressed,
    this.onLogoPressed,
    this.isLargeTitle = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPremium = ref.read(authUseCaseProvider).isPremium();
    final logoPath = isPremium
        ? 'assets/images/ic_cussme_premium.svg'
        : 'assets/images/ic_cussme.svg';
    return Column(
      children: [
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CussMeBackButton(
                    text: backButtonText,
                    onPressed: onBackPressed,
                  ),
                  const Spacer(),
                  InkWell(
                    onTap: () {
                      if (onLogoPressed != null) {
                        onLogoPressed!();
                      } else {
                        GoRouter.of(context).goToHome();
                      }
                    },
                    child: SvgPicture.asset(logoPath, height: 24),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    titleIcon,
                    color: Palette.onSurface,
                    size: isLargeTitle ? 24 : 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: isLargeTitle
                        ? TextStyles.titleLarge.copyWith(
                            fontWeight: FontWeight.w500,
                            color: Palette.onSurface,
                          )
                        : TextStyles.titleMedium.copyWith(
                            color: Palette.onSurface,
                          ),
                  ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: isLargeTitle ? 24 : 8),
        const Divider(height: 1, color: Palette.outlineVariant),
      ],
    );
  }
}
