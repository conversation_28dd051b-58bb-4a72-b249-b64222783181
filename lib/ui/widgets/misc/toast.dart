import 'package:flutter/material.dart';

import '../../styles/palette.dart';
import '../../styles/text_styles.dart';

class Toast extends StatelessWidget {
  final String message;
  final String? actionText;
  final VoidCallback? onAction;
  final VoidCallback? onDismiss;

  const Toast({
    super.key,
    required this.message,
    this.actionText,
    this.onAction,
    this.onDismiss,
  });

  static ScaffoldFeatureController<SnackBar, SnackBarClosedReason> show(
    BuildContext context,
    String message, {
    String? actionText,
    VoidCallback? onAction,
  }) {
    return showWithAction(
      context,
      message: message,
      actionLabel: actionText,
      onActionPressed: onAction,
    );
  }

  static ScaffoldFeatureController<SnackBar, SnackBarClosedReason> showWithAction(
    BuildContext context, {
    required String message,
    String? actionLabel,
    VoidCallback? onActionPressed,
  }) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    VoidCallback? wrappedOnAction;

    if (onActionPressed != null) {
      wrappedOnAction = () {
        onActionPressed();
        scaffoldMessenger.hideCurrentSnackBar();
      };
    }

    return scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Toast(
          message: message,
          actionText: actionLabel,
          onAction: wrappedOnAction,
        ),
        backgroundColor: Palette.inverseSurface,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 32),
        padding: EdgeInsets.zero,
        duration: const Duration(seconds: 3),
        dismissDirection: DismissDirection.horizontal,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              message,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.bodyMedium
                  .copyWith(color: Palette.inverseOnSurface),
            ),
          ),
          if (actionText != null && onAction != null)
            Padding(
              padding: const EdgeInsets.only(left: 12, right: 4),
              child: GestureDetector(
                onTap: onAction,
                child: Text(
                  actionText!,
                  style: TextStyles.labelLarge
                      .copyWith(color: Palette.inversePrimary),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
