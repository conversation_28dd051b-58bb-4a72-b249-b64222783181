import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class LanguageChip extends StatelessWidget {
  final String language;
  final bool isSelected;
  final VoidCallback onTap;

  const LanguageChip({
    super.key,
    required this.language,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? Palette.whiteD3 : null,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Palette.secondary : Palette.outlineVariant,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              language,
              style: TextStyles.bodyLarge.copyWith(
                color: isSelected ? Palette.secondary : Palette.onSurface,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              isSelected ? Icons.check : Icons.add,
              size: 18,
              color: isSelected ? Palette.secondary : Palette.onSurface,
            ),
          ],
        ),
      ),
    );
  }
}
