import 'package:cussme/domain/entities/language_entity.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class LanguageSelection extends StatelessWidget {
  final List<LanguageEntity> availableLanguages;
  final List<LanguageEntity> selectedLanguages;
  final Function(LanguageEntity) onLanguageSelected;

  const LanguageSelection({
    super.key,
    required this.availableLanguages,
    required this.selectedLanguages,
    required this.onLanguageSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: availableLanguages.map((language) {
        final isSelected = selectedLanguages.contains(language);
        return LanguageChip(
          language: language.name,
          isSelected: isSelected,
          onTap: () {
            onLanguageSelected(language);
          },
        );
      }).toList(),
    );
  }
}
