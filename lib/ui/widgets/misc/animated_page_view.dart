import 'package:flutter/material.dart';

class AnimatedPageView extends StatefulWidget {
  final int itemCount;
  final PageController? controller;
  final Clip clipBehavior;
  final bool padEnds;
  final EdgeInsetsGeometry? padding;
  final ValueChanged<int>? onPageChanged;
  final Widget Function(BuildContext context, int index, double pageOffset,
      double transitionProgress, int currentPageIndex) itemBuilder;

  const AnimatedPageView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.onPageChanged,
    this.clipBehavior = Clip.none,
    this.padEnds = false,
    this.padding,
  });

  @override
  State<AnimatedPageView> createState() => _AnimatedPageViewState();
}

class _AnimatedPageViewState extends State<AnimatedPageView> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = widget.controller ?? PageController(viewportFraction: .8);
    _pageController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _pageController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: PageView.builder(
        controller: _pageController,
        itemCount: widget.itemCount,
        clipBehavior: widget.clipBehavior,
        padEnds: widget.padEnds,
        onPageChanged: widget.onPageChanged,
        itemBuilder: (context, index) {
          var currentPage = _pageController.hasClients
              ? _pageController.page ?? _pageController.initialPage.toDouble()
              : 0.0;
          currentPage = currentPage * 100 / 75;
          final pageOffset = (index - currentPage);
          return _buildTransformedItem(
              context, index, pageOffset, currentPage.round());
        },
      ),
    );
  }

  Widget _buildTransformedItem(BuildContext context, int index,
      double pageOffset, int currentPageIndex) {
    final scale = _calculateScale(pageOffset);
    final transitionProgress = _calculateTransitionProgress(pageOffset);

    return Transform.translate(
      offset: const Offset(0, 0),
      child: Transform.scale(
        scale: scale,
        child: widget.itemBuilder(
            context, index, pageOffset, transitionProgress, currentPageIndex),
      ),
    );
  }

  double _calculateScale(double offset) {
    const double minScale = 0.85;
    const double maxScale = 1.0;

    final clamped = offset.abs().clamp(0.0, 1.0);
    return maxScale - (clamped * (maxScale - minScale));
  }

  double _calculateTransitionProgress(double offset) {
    final abs = offset.abs();
    return 1.0 - abs;
  }
}
