import 'dart:ui';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class BlurredWordListCardItem extends StatelessWidget {
  const BlurredWordListCardItem({
    super.key,
    required this.word,
  });

  final WordEntity word;

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Stack(
        children: [
          WordListCardItem(
            word: word,
            onTap: () {},
            onBookmarkTap: () {},
            onPlayPronunciation: () {},
          ),
          Positioned.fill(
            child: ClipRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 4.0, sigmaY: 4.0),
                child: Container(
                  margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(8)),
                    color: Palette.blurOverlay,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
