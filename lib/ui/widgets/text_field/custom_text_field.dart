import 'package:flutter/material.dart';
import '../../../ui/ui.dart';

class CustomTextField extends StatefulWidget {
  final String? label;
  final String? errorText;
  final bool obscureText;
  final bool togglePasswordVisibility;
  final Function(String)? onChanged;
  final Function()? onToggleVisibility;
  final TextInputType keyboardType;
  final TextCapitalization textCapitalization;
  final TextInputAction textInputAction;
  final bool ignoreErrorMessage;
  final bool enabled;
  final bool hasBorder;
  final EdgeInsetsGeometry? contentPadding;
  final String? initialText;
  final bool hasFocus;

  const CustomTextField({
    super.key,
    this.label,
    this.onChanged,
    this.errorText,
    this.obscureText = false,
    this.togglePasswordVisibility = false,
    this.onToggleVisibility,
    this.keyboardType = TextInputType.text,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction = TextInputAction.next,
    this.ignoreErrorMessage = true,
    this.enabled = true,
    this.hasBorder = true,
    this.hasFocus = false,
    this.initialText,
    this.contentPadding =
        const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();
    if (widget.hasFocus) {
      _focusNode.requestFocus();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(CustomTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update controller text if initialText changed and differs from current text
    if (widget.initialText != oldWidget.initialText &&
        widget.initialText != _controller.text) {
      _controller.text = widget.initialText ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool hasError = widget.errorText != null;
    // Only show error text if we're not ignoring it and it exists
    final bool showErrorText =
        !widget.ignoreErrorMessage && hasError && widget.errorText!.isNotEmpty;

    // Determine text color based on state (error, disabled, or normal)
    final Color textColor = hasError
        ? Palette.error
        : widget.enabled
            ? Palette.black1d
            : Palette.black1d.withValues(alpha: 0.4);

    final BorderRadius textFieldBorderRadius = BorderRadius.circular(4);

    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      onChanged: widget.onChanged,
      obscureText: widget.obscureText,
      keyboardType: widget.keyboardType,
      textCapitalization: widget.textCapitalization,
      textInputAction: widget.textInputAction,
      cursorColor: hasError ? Palette.error : Palette.black1d,
      style: TextStyles.bodyLarge.copyWith(color: textColor),
      enabled: widget.enabled,
      decoration: InputDecoration(
        labelText: widget.label,
        labelStyle: TextStyles.bodyLarge.copyWith(
          color: textColor,
        ),
        errorText: showErrorText ? widget.errorText : null,
        errorStyle: TextStyles.bodySmall.copyWith(
          color: Palette.error,
        ),
        border: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: textFieldBorderRadius,
                borderSide: const BorderSide(width: 1),
              )
            : InputBorder.none,
        enabledBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: textFieldBorderRadius,
                borderSide: BorderSide(
                    color: hasError ? Palette.error : Palette.black1d,
                    width: 1),
              )
            : InputBorder.none,
        disabledBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: textFieldBorderRadius,
                borderSide: BorderSide(
                    color: Palette.black1d.withValues(alpha: 0.12), width: 1),
              )
            : InputBorder.none,
        focusedBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: textFieldBorderRadius,
                borderSide: BorderSide(
                    color: hasError ? Palette.error : Palette.black1d,
                    width: 1),
              )
            : InputBorder.none,
        errorBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: textFieldBorderRadius,
                borderSide: const BorderSide(color: Palette.error, width: 1),
              )
            : InputBorder.none,
        focusedErrorBorder: widget.hasBorder
            ? OutlineInputBorder(
                borderRadius: textFieldBorderRadius,
                borderSide: const BorderSide(color: Palette.error, width: 1),
              )
            : InputBorder.none,
        contentPadding: widget.contentPadding,
        suffixIcon: widget.togglePasswordVisibility
            ? IconButton(
                icon: Icon(
                  widget.obscureText
                      ? Icons.visibility_off_outlined
                      : Icons.visibility_outlined,
                  color: hasError
                      ? Palette.error
                      : (widget.enabled
                          ? Palette.black1d
                          : Palette.black1d.withValues(alpha: 0.4)),
                ),
                onPressed: widget.enabled ? widget.onToggleVisibility : null,
              )
            : hasError
                ? const Icon(
                    Icons.error_outlined,
                    color: Palette.error,
                  )
                : null,
      ),
    );
  }
}
