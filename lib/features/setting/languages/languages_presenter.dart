import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/widgets/misc/screen_loading.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/languages_contract.dart';

part 'languages_presenter.g.dart';

@riverpod
class LanguagesPresenter extends _$LanguagesPresenter {
  StreamController<LanguagesSideEffect> sideEffects =
      StreamController<LanguagesSideEffect>();

  late final AuthUseCase _authUseCase;
  late final DataChangeTracker _dataChangeTracker;

  @override
  LanguagesState build() {
    _authUseCase = ref.read(authUseCaseProvider);
    _dataChangeTracker = ref.read(dataChangeTrackerProvider);

    ref.onDispose(() => sideEffects.close());

    state = const LanguagesState();
    _fetchInitialData();

    return state;
  }

  Future<void> _fetchInitialData() async {
    try {
      state = state.copyWith(loadingState: ScreenLoadingState.loading);

      final currentUser = _authUseCase.getCurrentUser();
      final allLanguages = await _authUseCase.getAllLanguages();

      final userLanguages = currentUser!.languages;

      state = LanguagesState(
        availableLanguages: allLanguages,
        selectedLanguages: [...userLanguages],
        preselectedLanguages: [...userLanguages],
        isLoading: false,
        loadingState: ScreenLoadingState.loaded,
      );

      _updateApplyButtonState();
    } catch (_) {
      state = state.copyWith(loadingState: ScreenLoadingState.error);
    }
  }

  void intentHandler(LanguagesIntent intent) {
    switch (intent) {
      case final SelectLanguageIntent intent:
        state = state.copyWith(
          selectedLanguages:
              state.selectedLanguages.toggleItem(intent.language),
        );
        _updateApplyButtonState();
        break;
      case RetryIntent _:
        _fetchInitialData();
        break;
      case ApplyChangesIntent _:
        _applyChanges();
        break;
      case NavigateBackIntent _:
        sideEffects.safeAdd(const LanguagesSideEffect.navigateBack());
        break;
    }
  }

  void _updateApplyButtonState() {
    state = state.copyWith(
      isApplyEnabled: state.selectedLanguages.isNotEmpty &&
          !state.preselectedLanguages.isEqualTo(state.selectedLanguages),
    );
  }

  void _applyChanges() async {
    if (!state.isApplyEnabled) return;

    try {
      state = state.copyWith(isLoading: true);

      final user =
          await _authUseCase.updateLanguages(state.selectedLanguages.copy());

      _dataChangeTracker.recordUpdate(TrackingFeatures.languages);
      state = state.copyWith(preselectedLanguages: [...user.languages]);
      sideEffects.safeAdd(
        LanguagesSideEffect.showMessage(
            Str.current.accountSettingsChangesSaved),
      );
    } on CussMeException catch (e) {
      sideEffects.safeAdd(LanguagesSideEffect.showMessage(e.toString()));
    } finally {
      state = state.copyWith(isLoading: false);
      _updateApplyButtonState();
    }
  }
}

@riverpod
Stream<LanguagesSideEffect> languagesSideEffects(Ref ref) {
  return ref.read(languagesPresenterProvider.notifier).sideEffects.stream;
}
