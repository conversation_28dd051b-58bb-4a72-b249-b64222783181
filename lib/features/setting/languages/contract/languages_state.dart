import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'languages_state.freezed.dart';

@freezed
sealed class LanguagesState with _$LanguagesState {
  const factory LanguagesState({
    @Default([]) List<LanguageEntity> selectedLanguages,
    @Default([]) List<LanguageEntity> preselectedLanguages,
    @Default([]) List<LanguageEntity> availableLanguages,
    @Default(false) bool isLoading,
    @Default(false) bool isApplyEnabled,
    @Default(ScreenLoadingState.loading) ScreenLoadingState loadingState,
  }) = _LanguagesState;
}
