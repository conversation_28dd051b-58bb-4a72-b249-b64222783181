import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'languages_intent.freezed.dart';

@freezed
sealed class LanguagesIntent with _$LanguagesIntent {
  const factory LanguagesIntent.selectLanguage(LanguageEntity language) =
      SelectLanguageIntent;
  const factory LanguagesIntent.retry() = RetryIntent;
  const factory LanguagesIntent.applyChanges() = ApplyChangesIntent;
  const factory LanguagesIntent.navigateBack() = NavigateBackIntent;
}
