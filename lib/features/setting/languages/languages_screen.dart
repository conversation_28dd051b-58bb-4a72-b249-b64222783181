import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../localization/generated/l10n.dart';
import 'contract/languages_contract.dart';
import 'languages_presenter.dart';

class LanguagesScreen extends ConsumerWidget {
  final String source;

  const LanguagesScreen({super.key, required this.source});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final LanguagesState state = ref.watch(languagesPresenterProvider);
    final presenter = ref.read(languagesPresenterProvider.notifier);

    return AdMobScaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          CussMeHeader(
            title: Str.of(context).chooseLanguage,
            titleIcon: Icons.language,
            backButtonText: source,
            onBackPressed: () => presenter.intentHandler(
              const LanguagesIntent.navigateBack(),
            ),
          ),
          Expanded(
            child: state.loadingState == ScreenLoadingState.loaded
                ? SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 32),
                          LanguageSelection(
                            availableLanguages: state.availableLanguages,
                            selectedLanguages: state.selectedLanguages,
                            onLanguageSelected: (language) {
                              presenter.intentHandler(
                                LanguagesIntent.selectLanguage(language),
                              );
                            },
                          ),
                          const SizedBox(height: 24),
                          Text(
                            Str.of(context).moreLanguagesComingSoon,
                            style: TextStyles.bodyMedium.copyWith(
                              color: Palette.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : ScreenLoading(
                    state: state.loadingState,
                    onRetry: () => presenter.intentHandler(
                      const LanguagesIntent.retry(),
                    ),
                  ),
          ),
          if (state.loadingState == ScreenLoadingState.loaded)
            ApplyButton(
              onPressed: () => presenter.intentHandler(
                const LanguagesIntent.applyChanges(),
              ),
              isEnabled: state.isApplyEnabled,
              isLoading: state.isLoading,
            ),
        ],
      ),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(languagesSideEffectsProvider, (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateBackSideEffect _:
            Navigator.pop(context);
            break;
          case final ShowMessageSideEffect se:
            Toast.show(context, se.message);
            break;
        }
      });
    });
  }
}
