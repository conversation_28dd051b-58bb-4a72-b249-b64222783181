import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/keyboard_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../localization/generated/l10n.dart';
import '../delete_account/delete_account_dialog.dart';
import 'account_settings_presenter.dart';
import 'contract/account_settings_contract.dart';

class AccountSettingsScreen extends ConsumerWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final state = ref.watch(accountSettingsPresenterProvider);
    final presenter = ref.read(accountSettingsPresenterProvider.notifier);

    return AdMobScaffold(
      body: Column(
        children: [
          CussMeHeader(
            title: Str.of(context).accountSettingsLabel,
            titleIcon: Icons.person_outline,
            backButtonText: Str.of(context).preferencesTitle,
            onBackPressed: () =>
                presenter.intentHandler(const AccountSettingsIntent.goBack()),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 36),
                  _buildForm(context, state, presenter),
                  const SizedBox(height: 36),
                  _buildAccountOptions(context, state, presenter),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
          ApplyButton(
            isEnabled: state.isApplyEnabled,
            isLoading: state.isLoading,
            onPressed: () =>
                presenter.intentHandler(const ApplyChangesIntent()),
          ),
        ],
      ),
    );
  }

  Widget _buildForm(
    BuildContext context,
    AccountSettingsState state,
    AccountSettingsPresenter presenter,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          CustomTextField(
            label: Str.of(context).firstNameLabel,
            onChanged: (value) =>
                presenter.intentHandler(FirstNameChangedIntent(value)),
            errorText: state.firstNameError,
            keyboardType: TextInputType.name,
            textCapitalization: TextCapitalization.words,
            initialText: state.firstName,
          ),
          const SizedBox(height: 12),
          CustomTextField(
            label: Str.of(context).lastNameLabel,
            onChanged: (value) =>
                presenter.intentHandler(LastNameChangedIntent(value)),
            errorText: state.lastNameError,
            keyboardType: TextInputType.name,
            textCapitalization: TextCapitalization.words,
            initialText: state.lastName,
          ),
          const SizedBox(height: 12),
          CustomTextField(
            label: Str.of(context).emailLabel,
            enabled: false,
            initialText: state.email,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountOptions(
    BuildContext context,
    AccountSettingsState state,
    AccountSettingsPresenter presenter,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PrimaryTextButton(
            padding:
                const EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 4),
            text: Str.of(context).resetPassword,
            isEnabled: state.isResetPasswordEnabled,
            disabledColor: Palette.inversePrimary,
            isShrinkWrap: true,
            clickableOnDisabled: false,
            onPressed: () =>
                presenter.intentHandler(const ResetPasswordIntent()),
          ),
          if (!state.isResetPasswordEnabled)
            Padding(
              padding: const EdgeInsets.only(left: 12, right: 12),
              child: Text(
                Str.of(context).providerPasswordResetError,
                style: TextStyles.bodySmall.copyWith(
                  color: Palette.onSurfaceVariant,
                ),
              ),
            ),
          const SizedBox(height: 20),
          PrimaryTextButton(
            padding: const EdgeInsets.only(left: 12, right: 12),
            text: Str.of(context).deleteAccount,
            textColor: Palette.secondary,
            onPressed: () =>
                presenter.intentHandler(const DeleteAccountIntent()),
          ),
        ],
      ),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(accountSettingsSideEffectsProvider, (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateBackSideEffect _:
            Navigator.pop(context);
            break;
          case NavigateToResetPasswordSideEffect _:
            GoRouter.of(context).pushToResetPasswordNew();
            break;
          case ShowDeleteAccountConfirmationSideEffect _:
            showDeleteAccountDialog(context);
            break;
          case HideKeyboardSideEffect _:
            KeyboardUtils.hideKeyboard(context);
            break;
          case final ShowMessageSideEffect sideEffect:
            Toast.show(context, sideEffect.message);
            break;
        }
      });
    });
  }
}
