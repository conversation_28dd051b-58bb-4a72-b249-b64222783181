import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_settings_intent.freezed.dart';

@freezed
sealed class AccountSettingsIntent with _$AccountSettingsIntent {
  const factory AccountSettingsIntent.firstNameChanged(String firstName) =
      FirstNameChangedIntent;
  const factory AccountSettingsIntent.lastNameChanged(String lastName) =
      LastNameChangedIntent;
  const factory AccountSettingsIntent.applyChanges() = ApplyChangesIntent;
  const factory AccountSettingsIntent.resetPassword() = ResetPasswordIntent;
  const factory AccountSettingsIntent.deleteAccount() = DeleteAccountIntent;
  const factory AccountSettingsIntent.goBack() = GoBackIntent;
}
