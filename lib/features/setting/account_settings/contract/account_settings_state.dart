import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_settings_state.freezed.dart';

@freezed
abstract class AccountSettingsState with _$AccountSettingsState {
  const factory AccountSettingsState({
    String? firstName,
    String? lastName,
    String? email,
    String? originalFirstName,
    String? originalLastName,
    @Default(false) bool isLoading,
    @Default(false) bool isApplyEnabled,
    @Default(false) bool isResetPasswordEnabled,
    String? firstNameError,
    String? lastNameError,
  }) = _AccountSettingsState;
}
