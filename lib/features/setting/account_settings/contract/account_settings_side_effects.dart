import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_settings_side_effects.freezed.dart';

@freezed
sealed class AccountSettingsSideEffect with _$AccountSettingsSideEffect {
  const factory AccountSettingsSideEffect.navigateBack() =
      NavigateBackSideEffect;

  const factory AccountSettingsSideEffect.showMessage(String message) =
      ShowMessageSideEffect;
  const factory AccountSettingsSideEffect.navigateToResetPassword() =
      NavigateToResetPasswordSideEffect;
  const factory AccountSettingsSideEffect.hideKeyboard() =
      HideKeyboardSideEffect;
  const factory AccountSettingsSideEffect.showDeleteAccountConfirmation() =
      ShowDeleteAccountConfirmationSideEffect;
}
