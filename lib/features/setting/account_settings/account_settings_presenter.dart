import 'dart:async';

import 'package:cussme/data/use_cases/auth_use_case.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/account_settings_contract.dart';

part 'account_settings_presenter.g.dart';

@riverpod
class AccountSettingsPresenter extends _$AccountSettingsPresenter {
  late final AuthUseCase _authUsecase;
  StreamController<AccountSettingsSideEffect> sideEffects =
      StreamController<AccountSettingsSideEffect>();

  @override
  AccountSettingsState build() {
    _authUsecase = ref.read(authUseCaseProvider);

    ref.onDispose(() => sideEffects.close());

    state = const AccountSettingsState();
    _loadUserData();

    return state;
  }

  void intentHandler(AccountSettingsIntent intent) {
    switch (intent) {
      case final FirstNameChangedIntent intent:
        state =
            state.copyWith(firstName: intent.firstName, firstNameError: null);
        _updateButtonState();
        break;
      case final LastNameChangedIntent intent:
        state = state.copyWith(lastName: intent.lastName, lastNameError: null);
        _updateButtonState();
        break;
      case ApplyChangesIntent _:
        _hideKeyboard();
        _applyChanges();
        break;
      case ResetPasswordIntent _:
        if (!state.isResetPasswordEnabled) return;

        _hideKeyboard();
        sideEffects
            .safeAdd(const AccountSettingsSideEffect.navigateToResetPassword());
        break;
      case DeleteAccountIntent _:
        _hideKeyboard();
        sideEffects.safeAdd(
            const AccountSettingsSideEffect.showDeleteAccountConfirmation());
        break;
      case GoBackIntent _:
        sideEffects.safeAdd(const AccountSettingsSideEffect.navigateBack());
        break;
    }
  }

  void _loadUserData() {
    try {
      final user = _authUsecase.getCurrentUser();
      final isResetPasswordEnabled = _authUsecase.canUserResetPassword();

      state = state.copyWith(
        firstName: user!.firstName,
        lastName: user.lastName,
        email: user.email,
        originalFirstName: user.firstName,
        originalLastName: user.lastName,
        isResetPasswordEnabled: isResetPasswordEnabled,
      );
    } catch (e) {
      sideEffects.safeAdd(AccountSettingsSideEffect.showMessage(e.toString()));
    } finally {
      _updateButtonState();
    }
  }

  void _updateButtonState() {
    final isDataChanged = _isUserDataChanged();
    final hasErrors =
        state.firstNameError != null || state.lastNameError != null;
    final hasRequiredData =
        state.firstName != null && state.firstName!.isNotEmpty;

    state = state.copyWith(
        isApplyEnabled: isDataChanged && !hasErrors && hasRequiredData);
  }

  bool _isUserDataChanged() {
    return state.firstName != state.originalFirstName ||
        state.lastName != state.originalLastName;
  }

  void _hideKeyboard() {
    sideEffects.safeAdd(const AccountSettingsSideEffect.hideKeyboard());
  }

  bool _validateFirstName() {
    if (state.firstName == null || state.firstName!.isEmpty) {
      state = state.copyWith(firstNameError: Str.current.firstNameRequired);
      _updateButtonState();
      return false;
    }
    return true;
  }

  void _applyChanges() async {
    if (!_validateFirstName()) return;
    try {
      state = state.copyWith(isLoading: true);

      final updatedUser = await _authUsecase.updateProfile(
        state.firstName!,
        state.lastName,
      );

      state = state.copyWith(
        originalFirstName: updatedUser.firstName,
        originalLastName: updatedUser.lastName,
      );

      sideEffects.safeAdd(AccountSettingsSideEffect.showMessage(
          Str.current.accountSettingsChangesSaved));
    } on CussMeException catch (e) {
      sideEffects.safeAdd(AccountSettingsSideEffect.showMessage(e.toString()));
    } finally {
      state = state.copyWith(isLoading: false);
      _updateButtonState();
    }
  }
}

@riverpod
Stream<AccountSettingsSideEffect> accountSettingsSideEffects(Ref ref) {
  return ref.read(accountSettingsPresenterProvider.notifier).sideEffects.stream;
}
