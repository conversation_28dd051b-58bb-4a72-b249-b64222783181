import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

import '../../../localization/generated/l10n.dart';

class SubInfoScreen extends StatelessWidget {
  const SubInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AdMobScaffold(
      body: Column(
        children: [
          CussMeHeader(
            title: Str.of(context).membershipInfoLabel,
            titleIcon: Icons.attach_money_outlined,
            backButtonText: Str.of(context).preferencesTitle,
            onBackPressed: () => Navigator.pop(context),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    Text(
                      Str.of(context).membershipInfoSubtitle,
                      style: TextStyles.titleMedium.copyWith(
                        color: Palette.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      Str.of(context).membershipInfoDescription,
                      style: TextStyles.bodyMedium.copyWith(
                        color: Palette.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
