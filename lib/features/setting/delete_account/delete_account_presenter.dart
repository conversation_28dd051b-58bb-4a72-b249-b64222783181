import 'dart:async';

import 'package:cussme/data/use_cases/auth_use_case.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/delete_account_contract.dart';

part 'delete_account_presenter.g.dart';

@riverpod
class DeleteAccountPresenter extends _$DeleteAccountPresenter {
  final StreamController<DeleteAccountSideEffect> sideEffects =
      StreamController<DeleteAccountSideEffect>();
  late final AuthUseCase _authUseCase;

  @override
  DeleteAccountState build() {
    _authUseCase = ref.read(authUseCaseProvider);
    ref.onDispose(() => sideEffects.close());
    return const DeleteAccountState();
  }

  void intentHandler(DeleteAccountIntent intent) {
    switch (intent) {
      case ConfirmDeleteIntent _:
        _deleteAccount();
        break;
      case CancelDeleteIntent _:
        sideEffects.safeAdd(const DeleteAccountSideEffect.closeDialog());
        break;
    }
  }

  Future<void> _deleteAccount() async {
    try {
      state = state.copyWith(isLoading: true);

      await _authUseCase.deleteUser();
    } on CussMeException catch (e) {
      sideEffects.safeAdd(DeleteAccountSideEffect.showMessage(e.toString()));
      sideEffects.safeAdd(const DeleteAccountSideEffect.closeDialog());
    }
  }
}

@riverpod
Stream<DeleteAccountSideEffect> deleteAccountSideEffects(Ref ref) {
  return ref.read(deleteAccountPresenterProvider.notifier).sideEffects.stream;
}
