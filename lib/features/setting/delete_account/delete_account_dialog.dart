import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../localization/generated/l10n.dart';

import 'contract/delete_account_contract.dart';
import 'delete_account_presenter.dart';

class DeleteAccountDialog extends ConsumerWidget {
  const DeleteAccountDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final state = ref.watch(deleteAccountPresenterProvider);
    final presenter = ref.read(deleteAccountPresenterProvider.notifier);

    return Dialog(
      backgroundColor: Palette.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(28),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Str.of(context).deleteAccountDialogTitle,
                  style: TextStyles.titleMedium.copyWith(
                    color: Palette.onSurface,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  Str.of(context).deleteAccountDialogMessage,
                  style: TextStyles.bodyMedium.copyWith(
                    color: Palette.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    PrimaryTextButton(
                        text: Str.of(context).cancel,
                        onPressed: () => presenter
                            .intentHandler(const DeleteAccountIntent.cancel()),
                        isEnabled: !state.isLoading,
                        padding:
                            const EdgeInsets.symmetric(horizontal: 12, vertical: 10)),
                    const SizedBox(width: 8),
                    Flexible(
                      child: PrimaryTextButton(
                        text: Str.of(context).yesDeleteAccount,
                        onPressed: () => presenter.intentHandler(
                            const DeleteAccountIntent.confirmDelete()),
                        isLoading: state.isLoading,
                        hasBorder: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

void _handleSideEffects(BuildContext context, WidgetRef ref) {
  ref.listen(deleteAccountSideEffectsProvider, (_, next) {
    next.whenData((sideEffect) {
      switch (sideEffect) {
        case NavigateToSignInSideEffect _:
          GoRouter.of(context).goToSignIn();
          break;
        case final ShowMessageSideEffect intent:
          Toast.show(context, intent.message);
          break;
        case CloseDialogSideEffect _:
          Navigator.of(context).pop();
          break;
      }
    });
  });
}

void showDeleteAccountDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const DeleteAccountDialog(),
  );
}
