import 'package:freezed_annotation/freezed_annotation.dart';

part 'delete_account_side_effects.freezed.dart';

@freezed
sealed class DeleteAccountSideEffect with _$DeleteAccountSideEffect {
  const factory DeleteAccountSideEffect.navigateToSignIn() =
      NavigateToSignInSideEffect;
  const factory DeleteAccountSideEffect.showMessage(String message) =
      ShowMessageSideEffect;
  const factory DeleteAccountSideEffect.closeDialog() = CloseDialogSideEffect;
}
