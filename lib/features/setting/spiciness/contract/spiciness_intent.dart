import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../domain/enums/spiciness.dart';

part 'spiciness_intent.freezed.dart';

@freezed
sealed class SpicinessIntent with _$SpicinessIntent {
  const factory SpicinessIntent.toggleSpiciness(Spiciness spiciness) =
      ToggleSpicinessIntent;
  const factory SpicinessIntent.applyChanges() = ApplyChangesIntent;
  const factory SpicinessIntent.navigateBack() = NavigateBackIntent;
}
