import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'spiciness_state.freezed.dart';

@freezed
sealed class SpicinessState with _$SpicinessState {
  const factory SpicinessState({
    @Default([]) List<Spiciness> selectedSpiciness,
    @Default([]) List<Spiciness> defaultSpiciness,
    @Default(false) bool isLoading,
    @Default(false) bool isApplyEnabled,
  }) = _SpicinessState;
}
