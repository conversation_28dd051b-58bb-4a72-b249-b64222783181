import 'dart:async';

import 'package:cussme/data/use_cases/auth_use_case.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/preferences_contract.dart';

part 'preferences_presenter.g.dart';

@riverpod
class PreferencesPresenter extends _$PreferencesPresenter {
  final StreamController<PreferencesSideEffect> sideEffects =
      StreamController<PreferencesSideEffect>();
  late final AuthUseCase _authUseCase;

  @override
  PreferencesState build() {
    _authUseCase = ref.read(authUseCaseProvider);
    ref.onDispose(() => sideEffects.close());

    final isGuest = _authUseCase.isGuest();
    final isPremium = _authUseCase.isPremium();

    return PreferencesState(
        initialized: true, isGuest: isGuest, isPremium: isPremium);
  }

  void intentHandler(PreferencesIntent intent) {
    switch (intent) {
      case NavigateToChooseLanguageIntent _:
        sideEffects
            .safeAdd(const PreferencesSideEffect.navigateToChooseLanguage());
        break;
      case NavigateToChooseApicinessIntent _:
        sideEffects
            .safeAdd(const PreferencesSideEffect.navigateToChooseSpiciness());
        break;
      case NavigateToAccountSettingsIntent _:
        sideEffects
            .safeAdd(const PreferencesSideEffect.navigateToAccountSettings());
        break;
      case NavigateToPremiumIntent _:
        sideEffects.safeAdd(const PreferencesSideEffect.navigateToPremium());
        break;
      case GoBackIntent _:
        sideEffects.safeAdd(const PreferencesSideEffect.goBack());
        break;
    }
  }
}

@riverpod
Stream<PreferencesSideEffect> preferencesSideEffects(Ref ref) {
  return ref.read(preferencesPresenterProvider.notifier).sideEffects.stream;
}
