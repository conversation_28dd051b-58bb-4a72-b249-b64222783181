import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/url_handler.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

class AboutScreen extends StatefulWidget {
  final bool isPremium;
  const AboutScreen({super.key, required this.isPremium});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdMobScaffold(
      body: Column(
        children: [
          _buildHeader(context),
          _buildTabBar(context),
          const Divider(height: 1, color: Palette.outlineVariant),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAboutCussmeTab(context),
                _buildTheStoryTab(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) => Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CussMeBackButton(
              text: Str.of(context).backButtonText,
              onPressed: () => context.pop(),
            ),
            const Spacer(),
            InkWell(
              onTap: () => GoRouter.of(context).goToHome(),
              child: SvgPicture.asset(
                widget.isPremium
                    ? 'assets/images/ic_cussme_premium.svg'
                    : 'assets/images/ic_cussme.svg',
                height: 24,
              ),
            ),
          ],
        ),
      );

  Widget _buildTabBar(BuildContext context) => TabBar(
        controller: _tabController,
        indicatorColor: Palette.primary,
        indicatorWeight: 2,
        labelColor: Palette.black1d,
        unselectedLabelColor: Palette.onSurface,
        labelStyle: TextStyles.labelLarge,
        unselectedLabelStyle: TextStyles.labelLarge,
        indicatorSize: TabBarIndicatorSize.tab,
        tabs: [
          Tab(text: Str.of(context).aboutCussmeTab),
          Tab(text: Str.of(context).theStoryTab),
        ],
      );

  Widget _buildAboutCussmeTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const SizedBox(height: 8),
          SvgPicture.asset(
            'assets/images/splash_logo.svg',
            width: 75,
            height: 50,
          ),
          const SizedBox(height: 8),
          Text(
            Str.of(context).aboutCussmeAppTitle,
            style: TextStyles.headlineSmall.copyWith(
              color: Palette.black1d,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () => openInBrowser(privacyPolicyUrl),
                child: Text(
                  Str.of(context).privacyPolicy,
                  style: TextStyles.bodyMedium.copyWith(
                    color: Palette.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                Str.of(context).linkSeparator,
                style: TextStyles.bodyMedium
                    .copyWith(color: Palette.onSurfaceVariant),
              ),
              GestureDetector(
                onTap: () => openInBrowser(termsAndConditionsUrl),
                child: Text(
                  Str.of(context).termsAndConditions,
                  style: TextStyles.bodyMedium.copyWith(
                    color: Palette.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                Str.of(context).linkSeparator,
                style: TextStyles.bodyMedium
                    .copyWith(color: Palette.onSurfaceVariant),
              ),
              GestureDetector(
                onTap: () => openInBrowser(eulaUrl),
                child: Text(
                  Str.of(context).eula,
                  style: TextStyles.bodyMedium.copyWith(
                    color: Palette.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Text(
            Str.of(context).aboutAppDescription1,
            style: TextStyles.bodyLarge.copyWith(color: Palette.onSurface),
            textAlign: TextAlign.left,
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/about_character_1.png',
                width: 95,
                height: 52,
              ),
              const SizedBox(width: 16),
              Image.asset(
                'assets/images/about_character_2.png',
                width: 84,
                height: 52,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            Str.of(context).aboutAppDescription2,
            style: TextStyles.bodyLarge.copyWith(color: Palette.onSurface),
            textAlign: TextAlign.left,
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildTheStoryTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 24),
                  GestureDetector(
                    onTap: () => openInBrowser(abishekProfileUrl),
                    child: Image.asset(
                      'assets/images/abishek_avatar.png',
                      width: 100,
                      height: 100,
                    ),
                  ),
                  const SizedBox(height: 28),
                  RichText(
                    textAlign: TextAlign.left,
                    text: TextSpan(
                      style: TextStyles.bodyLarge
                          .copyWith(color: Palette.onSurface),
                      children: [
                        TextSpan(text: Str.of(context).theStoryDescription1),
                        TextSpan(
                          text: Str.of(context).theStoryDescription2,
                          style: TextStyles.bodyLarge
                              .copyWith(color: Palette.primary),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () => openInBrowser(abishekProfileUrl),
                        ),
                        TextSpan(text: Str.of(context).theStoryDescription3),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          _builTeamSection(context),
        ],
      ),
    );
  }

  Widget _builTeamSection(BuildContext context) => Column(
        children: [
          const Divider(color: Palette.outlineVariant, height: 12),
          const SizedBox(height: 14),
          Text(
            Str.of(context).specialThanksTo,
            style: TextStyles.titleSmall.copyWith(
              color: Palette.onSurface,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 14),
          _buildTeamMember(
            context,
            'assets/images/shakib_profile.png',
            Str.of(context).shakibHabibiName,
            Str.of(context).leadDeveloperRole,
            shakibProfileUrl,
          ),
          const SizedBox(height: 24),
          _buildTeamMember(
            context,
            'assets/images/mozy_profile.png',
            Str.of(context).mozyMervinName,
            Str.of(context).consultantProductOwnerRole,
            mozyProfileUrl,
          ),
          const SizedBox(height: 24),
        ],
      );

  Widget _buildTeamMember(BuildContext context, String imagePath, String name,
      String role, String url) {
    return GestureDetector(
      onTap: () => openInBrowser(url),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: AssetImage(imagePath),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyles.titleSmall.copyWith(
                    color: Palette.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  role,
                  style: TextStyles.bodyMedium
                      .copyWith(color: Palette.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
