import 'package:cussme/domain/domain.dart';
import 'package:cussme/features/home/<USER>/language_list_item.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'contract/search_contract.dart';
import 'search_presenter.dart';

class SearchScreen extends ConsumerWidget {
  const SearchScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(searchPresenterProvider(key!));
    final presenter = ref.read(searchPresenterProvider(key!).notifier);
    _handleSideEffects(context, ref);

    return AdMobScaffold(
      body: Column(
        children: [
          _buildSearchHeader(presenter, state),
          Expanded(
            child: state.loadingState == ScreenLoadingState.loaded
                ? _buildSearchContent(context, state, presenter)
                : ScreenLoading(
                    state: state.loadingState,
                    onRetry: () =>
                        presenter.intentHandler(const SearchIntent.retry()),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader(SearchPresenter presenter, SearchState state) {
    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: 0),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(
                  Icons.arrow_back,
                  color: Palette.onSurface,
                  size: 24,
                ),
                onPressed: () =>
                    presenter.intentHandler(const SearchIntent.goBack()),
              ),
              Expanded(
                child: CustomTextField(
                  initialText: state.query,
                  onChanged: (value) =>
                      presenter.intentHandler(SearchIntent.search(value)),
                  hasBorder: false,
                  hasFocus: true,
                  contentPadding: const EdgeInsets.only(
                      left: 4, right: 8, top: 12, bottom: 12),
                ),
              ),
              if (state.query.isNotEmpty)
                IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Palette.onSurface,
                    size: 24,
                  ),
                  onPressed: () {
                    presenter.intentHandler(const SearchIntent.clearSearch());
                  },
                ),
            ],
          ),
          const SizedBox(height: 8),
          const Divider(height: 1, color: Palette.outlineVariant),
        ],
      ),
    );
  }

  Widget _buildSearchContent(
    BuildContext context,
    SearchState state,
    SearchPresenter presenter,
  ) {
    if (!state.hasResults && state.query.isNotEmpty) {
      return _buildEmptyState(context);
    }

    if (!state.hasResults) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: [
        ListView.separated(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.only(bottom: 8),
          itemCount: state.searchResults.length,
          separatorBuilder: (context, index) {
            final currentItem = state.searchResults[index];

            if (currentItem is ListItemCategory ||
                currentItem is ListItemHeader) {
              return const SizedBox.shrink();
            }
            return const Divider(height: 1, color: Palette.borderLight);
          },
          itemBuilder: (context, index) {
            final item = state.searchResults[index];

            if (item is ListItemCategory) {
              return _buildCategoryItem(item.header);
            } else if (item is ListItemHeader) {
              return _buildHeaderItem(item.header);
            } else if (item is ListItemWord) {
              return InkWell(
                onTap: () => presenter
                    .intentHandler(SearchIntent.selectWord(item.word.id)),
                child: WordListItem(
                  word: item.word,
                  onTap: () => presenter
                      .intentHandler(SearchIntent.selectWord(item.word.id)),
                ),
              );
            } else if (item is ListItemBlurredWord) {
              return BlurredWordListItem(word: item.word);
            } else if (item is ListItemLanguage) {
              return LanguageListItem(
                language: item.language,
                onTap: () => presenter
                    .intentHandler(SelectLanguageIntent(item.language)),
              );
            }

            return const SizedBox.shrink();
          },
        ),
        if (!state.isPremium)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: PremiumWarningWidget(
              onSubscribeTap: () => presenter
                  .intentHandler(const SearchIntent.navigateToPremium()),
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              Str.of(context).searchEmptyResultsTitle,
              style: TextStyles.titleMedium.copyWith(color: Palette.black1d),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              Str.of(context).searchEmptyResultsMessage,
              style: TextStyles.bodyMedium.copyWith(color: Palette.onSurface),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(String header) {
    final isLanguage = header == Str.current.languages;
    return Container(
      padding: EdgeInsets.fromLTRB(16, 16, 16, isLanguage ? 16 : 0),
      child: Text(
        header,
        style: TextStyles.titleMedium.copyWith(color: Palette.onSurface),
      ),
    );
  }

  Widget _buildHeaderItem(String header) {
    return Row(
      children: [
        Container(
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
          decoration: BoxDecoration(
            color: Palette.whiteD3,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Palette.secondary, width: 1),
          ),
          child: Center(
            child: Text(
              header,
              style: TextStyles.labelLarge.copyWith(color: Palette.primary),
            ),
          ),
        ),
      ],
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(searchSideEffectsProvider(key!), (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case final NavigateToWordDetailSideEffect se:
            GoRouter.of(context).pushToWordDetail(wordId: se.wordId);
            break;
          case final NavigateToWordListSideEffect se:
            GoRouter.of(context).pushToWordList(
              language: se.language,
              source: Str.of(context).searchTitle,
            );
            break;
          case GoBackSideEffect _:
            Navigator.of(context).pop();
            break;
          case final ShowMessageSideEffect se:
            Toast.show(context, se.message);
            break;
          case final NavigateToPremiumSideEffect se:
            GoRouter.of(context).pushToPremium(se.isGuest);
            break;
        }
      });
    });
  }
}
