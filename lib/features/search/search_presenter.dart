import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/search_contract.dart';

part 'search_presenter.g.dart';

@riverpod
class SearchPresenter extends _$SearchPresenter {
  StreamController<SearchSideEffect> sideEffects =
      StreamController<SearchSideEffect>();

  late final WordUseCase _wordUseCase;
  late final AuthUseCase _authUseCase;
  Timer? _debounceTimer;

  @override
  SearchState build(Key key) {
    _wordUseCase = ref.read(wordUseCaseProvider);
    _authUseCase = ref.read(authUseCaseProvider);

    ref.onDispose(() {
      sideEffects.close();
      _debounceTimer?.cancel();
    });

    final isGuest = _authUseCase.isGuest();
    final isPremium = _authUseCase.isPremium();

    return SearchState(isGuest: isGuest, isPremium: isPremium);
  }

  void intentHandler(SearchIntent intent) {
    switch (intent) {
      case final SearchQueryIntent intent:
        _handleSearchQuery(intent.query);
        break;
      case ClearSearchIntent _:
        _clearSearch();
        break;
      case GoBackIntent _:
        sideEffects.safeAdd(const SearchSideEffect.goBack());
        break;
      case final SelectWordIntent intent:
        sideEffects
            .safeAdd(SearchSideEffect.navigateToWordDetail(intent.wordId));
        break;
      case final SelectLanguageIntent intent:
        sideEffects
            .safeAdd(SearchSideEffect.navigateToWordList(intent.language));
        break;
      case RetryIntent _:
        _handleSearchQuery(state.query);
        break;
      case NavigateToPremiumIntent _:
        sideEffects.safeAdd(SearchSideEffect.navigateToPremium(state.isGuest));
        break;
    }
  }

  void _handleSearchQuery(String query) {
    state = state.copyWith(query: query);

    _debounceTimer?.cancel();
    if (query.isEmpty) {
      _clearSearch();
      return;
    }

    state = state.copyWith(loadingState: ScreenLoadingState.loading);

    _debounceTimer = Timer(const Duration(milliseconds: 300), () async {
      try {
        final results = await _wordUseCase.search(query: query);

        state = state.copyWith(
            searchResults: results, loadingState: ScreenLoadingState.loaded);
      } catch (_) {
        state = state.copyWith(loadingState: ScreenLoadingState.error);
      }
    });
  }

  void _clearSearch() {
    state = state.copyWith(
      query: '',
      searchResults: [],
      loadingState: ScreenLoadingState.loaded,
    );
  }
}

@riverpod
Stream<SearchSideEffect> searchSideEffects(Ref ref, Key key) {
  final presenter = ref.read(searchPresenterProvider(key).notifier);
  return presenter.sideEffects.stream;
}
