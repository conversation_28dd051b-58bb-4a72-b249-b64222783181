import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'report_intent.freezed.dart';

@freezed
sealed class ReportIntent with _$ReportIntent {
  const factory ReportIntent.close() = CloseIntent;
  const factory ReportIntent.reasonSelected(ReportReason reason) = ReasonSelectedIntent;
  const factory ReportIntent.detailChanged(String detail) = DetailChangedIntent;
  const factory ReportIntent.sendReport() = SendReportIntent;
}
