import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'report_state.freezed.dart';

@freezed
sealed class ReportState with _$ReportState {
  const factory ReportState({
    required WordEntity word,
    @Default(null) ReportReason? selectedReason,
    @Default('') String detail,
    @Default(false) bool isLoading,
    @Default(false) bool isSendEnabled,
  }) = _ReportState;
}
