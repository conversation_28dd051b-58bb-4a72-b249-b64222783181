import 'package:freezed_annotation/freezed_annotation.dart';

part 'report_side_effects.freezed.dart';

@freezed
sealed class ReportSideEffect with _$ReportSideEffect {
  const factory ReportSideEffect.close() = CloseSideEffect;
  const factory ReportSideEffect.showMessage(String message) =
      ShowMessageSideEffect;
  const factory ReportSideEffect.showSuccessDialog() =
      ShowSuccessDialogSideEffect;
}
