import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'contract/report_contract.dart';
import 'report_presenter.dart';

class ReportScreen extends ConsumerWidget {
  final WordEntity word;

  const ReportScreen({
    super.key,
    required this.word,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(reportPresenterProvider(word: word));
    final presenter = ref.read(reportPresenterProvider(word: word).notifier);
    _handleSideEffects(context, ref);

    return AdMobScaffold(
      backgroundColor: Palette.surface,
      hasBottomSheetBackground: true,
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.fromLTRB(4, 16, 12, 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  color: Palette.onSurface,
                  onPressed: () => presenter.intentHandler(
                    const ReportIntent.close(),
                  ),
                ),
                Expanded(
                  child: Text(
                    Str.of(context).reportTitle,
                    style: TextStyles.titleLarge.copyWith(
                      color: Palette.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildReasonDropdown(context, state, presenter),
                  const SizedBox(height: 20),
                  TextField(
                    maxLines: 5,
                    decoration: InputDecoration(
                      labelText: Str.of(context).reportDetailLabel,
                      border: const OutlineInputBorder(),
                      alignLabelWithHint: true,
                      floatingLabelAlignment: FloatingLabelAlignment.start,
                    ),
                    onChanged: (value) {
                      presenter.intentHandler(
                        ReportIntent.detailChanged(value),
                      );
                    },
                  ),
                  const SizedBox(height: 24),
                  Align(
                    alignment: Alignment.centerRight,
                    child: MinSizedTextButton(
                      text: Str.of(context).reportSendButton,
                      onPressed: () => presenter.intentHandler(
                        const ReportIntent.sendReport(),
                      ),
                      minSize: const Size(120, 40),
                      isEnabled: state.isSendEnabled,
                      isLoading: state.isLoading,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReasonDropdown(
    BuildContext context,
    ReportState state,
    ReportPresenter presenter,
  ) {
    return Row(
      children: [
        DropdownButtonHideUnderline(
          child: DropdownButton<ReportReason>(
            value: state.selectedReason,
            hint: Text(
              Str.of(context).reportReasonLabel,
              style: TextStyles.bodyMedium.copyWith(
                color: Palette.onSurface,
              ),
            ),
            isDense: true,
            icon: const Icon(Icons.arrow_drop_down),
            iconSize: 24,
            elevation: 16,
            style: TextStyles.bodyMedium.copyWith(color: Palette.onSurface),
            onChanged: (ReportReason? newValue) {
              if (newValue != null) {
                presenter.intentHandler(
                  ReportIntent.reasonSelected(newValue),
                );
              }
            },
            items: ReportReason.values
                .map<DropdownMenuItem<ReportReason>>((ReportReason value) {
              return DropdownMenuItem<ReportReason>(
                value: value,
                child: Text(value.getTitle()),
              );
            }).toList(),
          ),
        ),
        const Spacer(),
      ],
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(reportSideEffectsProvider(word: word), (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case CloseSideEffect _:
            Navigator.of(context).pop();
            break;
          case ShowMessageSideEffect _:
            Toast.show(context, sideEffect.message);
            break;
          case ShowSuccessDialogSideEffect _:
            SuccessDialog.show(context, Str.of(context).reportSentTitle);
            break;
        }
      });
    });
  }
}
