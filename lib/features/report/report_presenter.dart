import 'dart:async';

import 'package:cussme/data/use_cases/word_use_case.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/report_contract.dart';

part 'report_presenter.g.dart';

@riverpod
class ReportPresenter extends _$ReportPresenter {
  late StreamController<ReportSideEffect> sideEffects =
      StreamController<ReportSideEffect>();

  late final WordUseCase _wordUseCase;

  @override
  ReportState build({required WordEntity word}) {
    _wordUseCase = ref.read(wordUseCaseProvider);

    ref.onDispose(() => sideEffects.close());

    state = ReportState(word: word);

    return state;
  }

  void intentHandler(ReportIntent intent) {
    switch (intent) {
      case CloseIntent _:
        sideEffects.safeAdd(const ReportSideEffect.close());
        break;
      case final ReasonSelectedIntent intent:
        state = state.copyWith(selectedReason: intent.reason);
        _updateSendButtonState();
        break;
      case final DetailChangedIntent intent:
        state = state.copyWith(detail: intent.detail);
        _updateSendButtonState();
        break;
      case SendReportIntent _:
        _submitReport();
        break;
    }
  }

  void _updateSendButtonState() {
    final hasReason = state.selectedReason != null;
    state = state.copyWith(isSendEnabled: hasReason);
  }

  Future<void> _submitReport() async {
    if (!state.isSendEnabled) return;

    try {
      state = state.copyWith(isLoading: true);

      await _wordUseCase.submitWordReport(
        wordId: state.word.id,
        reason: state.selectedReason ?? ReportReason.other,
        detail: state.detail,
      );

      sideEffects.safeAdd(const ReportSideEffect.showSuccessDialog());
    } on CussMeException catch (e) {
      sideEffects.safeAdd(ReportSideEffect.showMessage(e.message));
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}

@riverpod
Stream<ReportSideEffect> reportSideEffects(Ref ref,
    {required WordEntity word}) {
  return ref
      .watch(reportPresenterProvider(word: word).notifier)
      .sideEffects
      .stream;
}
