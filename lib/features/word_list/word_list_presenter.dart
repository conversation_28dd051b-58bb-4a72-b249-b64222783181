import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/word_list_intent.dart';
import 'contract/word_list_side_effects.dart';
import 'contract/word_list_state.dart';

part 'word_list_presenter.g.dart';

@riverpod
class WordListPresenter extends _$WordListPresenter {
  late StreamController<WordListSideEffect> sideEffects =
      StreamController<WordListSideEffect>();

  late final WordUseCase _wordUseCase;
  late final AuthUseCase _authUseCase;
  late final DataChangeTracker _dataChangeTracker;

  @override
  WordListState build({required Key key, required LanguageEntity language}) {
    _wordUseCase = ref.read(wordUseCaseProvider);
    _authUseCase = ref.read(authUseCaseProvider);
    _dataChangeTracker = ref.read(dataChangeTrackerProvider);

    ref.onDispose(() => sideEffects.close());

    state = WordListState(language: language, isInitialLoading: true);
    _initializeData();

    return state;
  }

  Future<void> _initializeData() async {
    _checkIfUserIsGuest();
    _initializeSpiciness();
    await _loadWords();
  }

  void _checkIfUserIsGuest() {
    final isGuest = _authUseCase.isGuest();
    UserEntity? user;

    if (!isGuest) {
      user = _authUseCase.getCurrentUser();
    }

    state = state.copyWith(isGuest: isGuest, user: user);
  }

  void _initializeSpiciness() {
    try {
      List<Spiciness> initialSpiciness = [];

      if (state.isGuest) {
        initialSpiciness = [Spiciness.values.first];
      } else {
        final user = _authUseCase.getCurrentUser();
        initialSpiciness = user?.spiciness ?? [Spiciness.values.first];
      }

      state = state.copyWith(selectedSpiciness: initialSpiciness);
    } catch (_) {
      state = state.copyWith(loadingState: ScreenLoadingState.error);
    }
  }

  Future<void> _loadWords() async {
    try {
      if (state.selectedSpiciness.isEmpty) {
        sideEffects.safeAdd(
            WordListSideEffect.showMessage(Str.current.somethingWentWrong));
        return;
      }

      state = state.copyWith(
          loadingState: ScreenLoadingState.loading, showStickyWarning: false);
      _dataChangeTracker.recordFetch(key.toString());

      final groupedWords = await _wordUseCase.getGroupedFilteredWords(
        languageId: state.language.id,
        spiciness: state.selectedSpiciness,
      );

      state = state.copyWith(
        groupedWordItems: groupedWords,
        loadingState: ScreenLoadingState.loaded,
        isInitialLoading: false,
      );
    } on CussMeException catch (_) {
      state = state.copyWith(loadingState: ScreenLoadingState.error);
    }
  }

  void intentHandler(WordListIntent intent) {
    switch (intent) {
      case GoBackIntent _:
        sideEffects.safeAdd(const WordListSideEffect.goBack());
        break;
      case RetryIntent _:
        _loadWords();
        break;
      case final ToggleSpicinessIntent intent:
        _toggleSpiciness(intent.spiciness);
        break;
      case final NavigateToWordDetailIntent intent:
        sideEffects
            .safeAdd(WordListSideEffect.navigateToWordDetail(intent.wordId));
        break;
      case final ToggleBookmarkIntent intent:
        if (state.isGuest) {
          sideEffects.safeAdd(const WordListSideEffect.navigateToSignIn());
          return;
        }
        _toggleBookmark(intent.word.id, intent.word.isBookmarked);
        break;
      case NavigateToBookmarksIntent _:
        sideEffects.safeAdd(const WordListSideEffect.navigateToBookmarks());
        break;
      case ReturnedIntent _:
        if (_dataChangeTracker.shouldRefresh(
          key.toString(),
          TrackingFeatures.bookmark,
        )) {
          _loadWords();
        }
        break;
      case final PlayPronunciationIntent intent:
        _playPronunciation(intent.word);
        break;
      case NavigateToSearchIntent _:
        sideEffects.safeAdd(const WordListSideEffect.navigateToSearch());
        break;
      case NavigateToPremiumIntent _:
        sideEffects
            .safeAdd(WordListSideEffect.navigateToPremium(state.isGuest));
        break;
      case final UpdateStickyWarningIntent intent:
        state = state.copyWith(showStickyWarning: intent.show);
        break;
    }
  }

  Future<void> _playPronunciation(WordEntity word) async {
    try {
      await _wordUseCase.speak(word);
    } on PremiumRequiredException catch (e) {
      sideEffects.safeAdd(WordListSideEffect.navigateToPremium(e.isGuest));
    }
  }

  void _toggleSpiciness(Spiciness spiciness) {
    final currentSpiciness = List<Spiciness>.from(state.selectedSpiciness);

    if (currentSpiciness.contains(spiciness)) {
      if (currentSpiciness.length <= 1) {
        sideEffects.safeAdd(WordListSideEffect.showMessage(
            Str.current.spicinessSelectionRequired));
        return;
      }
      currentSpiciness.remove(spiciness);
    } else {
      currentSpiciness.add(spiciness);
    }

    state = state.copyWith(selectedSpiciness: currentSpiciness);
    _loadWords();
  }

  Future<void> _toggleBookmark(
      String wordId, bool currentBookmarkStatus) async {
    try {
      state = state.updateBookmarkByWordId(wordId, !currentBookmarkStatus);

      final newBookmarkStatus =
          await _wordUseCase.toggleBookmark(wordId, currentBookmarkStatus);

      if (newBookmarkStatus != !currentBookmarkStatus) {
        state = state.updateBookmarkByWordId(wordId, currentBookmarkStatus);
        sideEffects
            .safeAdd(WordListSideEffect.showMessage(Str.current.bookmarkError));
        return;
      }

      sideEffects.safeAdd(WordListSideEffect.showMessageWithAction(
          newBookmarkStatus
              ? Str.current.bookmarkAddedSuccess
              : Str.current.bookmarkRemovedSuccess,
          Str.current.allBookmarks));
    } on CussMeException catch (e) {
      state = state.updateBookmarkByWordId(wordId, currentBookmarkStatus);
      sideEffects.safeAdd(WordListSideEffect.showMessage(e.message));
    }
  }
}

@riverpod
Stream<WordListSideEffect> wordListSideEffects(Ref ref,
    {required LanguageEntity language, required Key key}) {
  return ref
      .watch(wordListPresenterProvider(language: language, key: key).notifier)
      .sideEffects
      .stream;
}
