import 'package:freezed_annotation/freezed_annotation.dart';

part 'intro_side_effects.freezed.dart';

@freezed
sealed class IntroSideEffect with _$IntroSideEffect {
  const factory IntroSideEffect.navigateToHome() = NavigateToHomeSideEffect;
  const factory IntroSideEffect.navigateToPage(int pageIndex) =
      NavigateToPageSideEffect;
  const factory IntroSideEffect.navigateToSpiciness() =
      NavigateToSpicinessSideEffect;
  const factory IntroSideEffect.navigateToDestination() =
      NavigateToDestinationSideEffect;
  const factory IntroSideEffect.showMessage(String message) =
      ShowMessageSideEffect;
}
