import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class SpicinessSelectionPage extends StatelessWidget {
  final List<Spiciness> selectedSpiciness;
  final Function(Spiciness) onSpicinessToggled;

  const SpicinessSelectionPage({
    super.key,
    required this.selectedSpiciness,
    required this.onSpicinessToggled,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(minHeight: constraints.maxHeight),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top section
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 48),
                      TextWithHighlight(
                        text: Str.of(context).chooseSpicinessLevel,
                        highlightedText:
                            Str.of(context).chooseSpicinessLevelHighlight,
                        style: TextStyles.headlineSmall.copyWith(
                          color: Palette.primary,
                        ),
                        highlightStyle: TextStyles.headlineSmall.copyWith(
                          color: Palette.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        Str.of(context).canChangeSpicinessLater,
                        style: TextStyles.bodyMedium.copyWith(
                          color: Palette.secondary,
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                  // Centered section
                  SpicinessRadio(
                    selectedSpiciness: selectedSpiciness,
                    onSpicinessToggled: onSpicinessToggled,
                  ),
                  // Bottom section
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
