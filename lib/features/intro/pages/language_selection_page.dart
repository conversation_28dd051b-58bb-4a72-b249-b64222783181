import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class LanguageSelectionPage extends StatelessWidget {
  final List<LanguageEntity> availableLanguages;
  final List<LanguageEntity> selectedLanguages;
  final Function(LanguageEntity) onLanguageSelected;

  const LanguageSelectionPage({
    super.key,
    required this.availableLanguages,
    required this.selectedLanguages,
    required this.onLanguageSelected,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top section
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 48),
                      TextWithHighlight(
                        text: Str.of(context).chooseLanguagesForDashboard,
                        highlightedText: Str.of(context)
                            .chooseLanguagesForDashboardHighlight,
                        style: TextStyles.headlineSmall.copyWith(
                          color: Palette.primary,
                        ),
                        highlightStyle: TextStyles.headlineSmall.copyWith(
                          color: Palette.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        Str.of(context).canChangeLanguagesLater,
                        style: TextStyles.bodyMedium.copyWith(
                          color: Palette.secondary,
                        ),
                      ),
                      const SizedBox(height: 48),
                    ],
                  ),
                  // Centered section
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LanguageSelection(
                        availableLanguages: availableLanguages,
                        selectedLanguages: selectedLanguages,
                        onLanguageSelected: onLanguageSelected,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        Str.of(context).moreLanguagesComingSoon,
                        style: TextStyles.bodyMedium.copyWith(
                          color: Palette.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  // Bottom section
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
