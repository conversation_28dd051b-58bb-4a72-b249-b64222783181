import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'contract/intro_contract.dart';
import 'intro_presenter.dart';
import 'pages/language_selection_page.dart';
import 'pages/spiciness_selection_page.dart';

class IntroScreen extends ConsumerStatefulWidget {
  final NavigationData? navigationData;

  const IntroScreen({super.key, this.navigationData});

  @override
  ConsumerState<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends ConsumerState<IntroScreen> {
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _handleSideEffects(context, ref);

    final IntroState state = ref
        .watch(introPresenterProvider(navigationData: widget.navigationData));
    final presenter = ref.read(
        introPresenterProvider(navigationData: widget.navigationData).notifier);

    return AdMobScaffold(
      body: Column(
        children: [
          Expanded(
            child: state.loadingState == ScreenLoadingState.loaded
                ? _buildViewPager(state, presenter)
                : ScreenLoading(
                    state: state.loadingState,
                    onRetry: () => presenter.intentHandler(
                      const IntroIntent.retry(),
                    ),
                  ),
          ),
          if (state.loadingState == ScreenLoadingState.loaded)
            _buildBottomButtons(context, state, presenter),
          if (state.loadingState != ScreenLoadingState.loading)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: PrimaryTextButton(
                onPressed: () =>
                    presenter.intentHandler(const IntroIntent.skipToHome()),
                text: Str.of(context).skipToDashboard,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildViewPager(IntroState state, IntroPresenter presenter) {
    return PageView(
      controller: _pageController,
      physics: const NeverScrollableScrollPhysics(),
      onPageChanged: (index) =>
          presenter.intentHandler(IntroIntent.pageChanged(index)),
      children: [
        LanguageSelectionPage(
          availableLanguages: state.availableLanguages,
          selectedLanguages: state.selectedLanguages,
          onLanguageSelected: (language) =>
              presenter.intentHandler(IntroIntent.languageSelected(language)),
        ),
        SpicinessSelectionPage(
          selectedSpiciness: state.selectedSpiciness,
          onSpicinessToggled: (spiciness) =>
              presenter.intentHandler(IntroIntent.spicinessToggled(spiciness)),
        ),
      ],
    );
  }

  Widget _buildBottomButtons(
      BuildContext context, IntroState state, IntroPresenter presenter) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ApplyButton(
            padding: EdgeInsets.zero,
            isLoading: state.isLoading,
            isEnabled: state.isPrimaryButtonEnabled,
            onPressed: () => presenter
                .intentHandler(const IntroIntent.primaryButtonClicked()),
            text: state.primaryButtonText,
          ),
          const SizedBox(height: 12),
          PrimaryTextButton(
            text: state.secondaryButtonText,
            onPressed: () => presenter
                .intentHandler(const IntroIntent.secondaryButtonClicked()),
            hasBorder: true,
          ),
        ],
      ),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(introSideEffectsProvider(navigationData: widget.navigationData),
        (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateToHomeSideEffect _:
            GoRouter.of(context).goToHome();
            break;
          case final NavigateToDestinationSideEffect _:
            GoRouter.of(context)
                .goToDestinationWithHomeInStack(widget.navigationData!);
            break;
          case final NavigateToPageSideEffect intent:
            _pageController.animateToPage(
              intent.pageIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
            break;
          case NavigateToSpicinessSideEffect _:
            _pageController.animateToPage(
              1,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
            break;
          case final ShowMessageSideEffect se:
            Toast.show(context, se.message);
        }
      });
    });
  }
}
