import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/logout/logout_screen.dart';
import '../../localization/generated/l10n.dart';
import '../../routing/app_router.dart';
import '../../ui/ui.dart';

class MenuItem {
  final String title;
  final IconData? icon;
  final Widget? iconWidget;
  final VoidCallback? onTap;
  final bool isCenter;
  final bool isPremium;

  const MenuItem({
    required this.title,
    this.icon,
    this.iconWidget,
    this.onTap,
    this.isCenter = false,
    this.isPremium = false,
  });
}

class ProfileMenu extends ConsumerWidget {
  const ProfileMenu({
    super.key,
    required this.isGuest,
    this.isPremium = true,
  });

  final bool isGuest;
  final bool isPremium;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopupMenuButton<void>(
      icon: const Icon(
        Icons.account_circle_outlined,
        color: Palette.primary,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4.0),
      ),
      color: Colors.white,
      elevation: 4,
      offset: const Offset(-16, 40),
      itemBuilder: (BuildContext context) => isGuest
          ? _buildGuestMenuItems(context)
          : _buildUserMenuItems(context),
    );
  }

  List<PopupMenuEntry<void>> _buildGuestMenuItems(BuildContext context) {
    return _buildMenuItems([
      MenuItem(
        title: Str.of(context).profileMenuSignIn,
        icon: Icons.login_outlined,
        onTap: () => _navigateToSignIn(context),
      ),
      MenuItem(
        title: Str.of(context).profileMenuGoPremium,
        iconWidget: Image.asset(
          'assets/images/ic_premium_menu.png',
          width: 24,
          height: 24,
        ),
        isPremium: true,
        onTap: () => _navigateToPremium(context),
      ),
      MenuItem(
        title: Str.of(context).profileMenuAboutApp,
        onTap: () => _navigateToAbout(context),
        isCenter: true,
      ),
    ]);
  }

  List<PopupMenuEntry<void>> _buildUserMenuItems(BuildContext context) {
    return _buildMenuItems([
      MenuItem(
        title: Str.of(context).profileMenuPreferences,
        icon: Icons.settings_outlined,
        onTap: () => _navigateToPreferences(context),
      ),
      MenuItem(
        title: Str.of(context).profileMenuBookmarks,
        icon: Icons.bookmark_border_outlined,
        onTap: () => _navigateToBookmarks(context),
      ),
      MenuItem(
        title: Str.of(context).profileMenuLogout,
        icon: Icons.logout_outlined,
        onTap: () => _showLogoutDialog(context),
      ),
      if (!isPremium)
        MenuItem(
          title: Str.of(context).profileMenuGoPremium,
          iconWidget: Image.asset(
            'assets/images/ic_premium_menu.png',
            width: 24,
            height: 24,
          ),
          isPremium: true,
          onTap: () => _navigateToPremium(context),
        ),
      MenuItem(
        title: Str.of(context).profileMenuAboutApp,
        onTap: () => _navigateToAbout(context),
        isCenter: true,
      ),
    ]);
  }

  List<PopupMenuEntry<void>> _buildMenuItems(List<MenuItem> items) {
    final List<PopupMenuEntry<void>> menuEntries = [];

    for (int i = 0; i < items.length; i++) {
      menuEntries.add(_buildMenuItem(items[i]));

      // Add divider between items (but not after the last item)
      if (i < items.length - 1) {
        menuEntries.add(const PopupMenuDivider());
      }
    }

    return menuEntries;
  }

  PopupMenuItem<void> _buildMenuItem(MenuItem item) {
    if (item.isCenter || (item.icon == null && item.iconWidget == null)) {
      return _buildSimpleMenuItem(item);
    } else {
      return _buildMenuItemWithIcon(item);
    }
  }

  PopupMenuItem<void> _buildMenuItemWithIcon(MenuItem item) {
    return PopupMenuItem<void>(
      onTap: item.onTap != null
          ? () => Future.delayed(
                const Duration(milliseconds: 10),
                item.onTap,
              )
          : null,
      child: Row(
        children: [
          item.iconWidget ??
              Icon(
                item.icon,
                size: 24,
                color: Palette.onSurface,
              ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              item.title,
              style: TextStyles.bodyLarge.copyWith(
                color: item.isPremium ? Palette.primary : Palette.primaryDark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  PopupMenuItem<void> _buildSimpleMenuItem(MenuItem item) {
    return PopupMenuItem<void>(
      onTap: item.onTap != null
          ? () => Future.delayed(
                const Duration(milliseconds: 10),
                item.onTap,
              )
          : null,
      child: Align(
        alignment: Alignment.center,
        child: Text(
          item.title,
          style: TextStyles.bodyLarge.copyWith(
            color: Palette.primaryDark,
          ),
        ),
      ),
    );
  }

  void _navigateToSignIn(BuildContext context) =>
      GoRouter.of(context).pushToSignIn();

  void _navigateToPreferences(BuildContext context) =>
      GoRouter.of(context).pushToPreferences();

  void _navigateToBookmarks(BuildContext context) =>
      GoRouter.of(context).pushToBookmarks();

  void _showLogoutDialog(BuildContext context) => showLogoutDialog(context);

  void _navigateToAbout(BuildContext context) =>
      GoRouter.of(context).pushToAbout(isPremium);

  void _navigateToPremium(BuildContext context) =>
      GoRouter.of(context).pushToPremium(isGuest);
}
