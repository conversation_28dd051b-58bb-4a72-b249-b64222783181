import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'bookmarks_intent.freezed.dart';

@freezed
sealed class BookmarksIntent with _$BookmarksIntent {
  const factory BookmarksIntent.retry() = RetryIntent;
  const factory BookmarksIntent.goBack() = GoBackIntent;
  const factory BookmarksIntent.navigateToWordDetail(String wordId) =
      NavigateToWordDetailIntent;
  const factory BookmarksIntent.removeBookmark(String wordId) =
      RemoveBookmarkIntent;
  const factory BookmarksIntent.returned() = ReturnedIntent;
  const factory BookmarksIntent.playPronunciation(WordEntity word) =
      PlayPronunciationIntent;
}
