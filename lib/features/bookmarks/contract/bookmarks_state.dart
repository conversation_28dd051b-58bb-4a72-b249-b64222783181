import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'bookmarks_state.freezed.dart';

@freezed
sealed class BookmarksState with _$BookmarksState {
  const BookmarksState._();

  const factory BookmarksState({
    @Default([]) List<ListItem> groupedBookmarkItems,
    @Default(ScreenLoadingState.loading) ScreenLoadingState loadingState,
  }) = _BookmarksState;

  BookmarksState removeBookmark(String wordId) {
    final updatedItems = List<ListItem>.from(groupedBookmarkItems);
    final itemIndex = updatedItems.indexWhere(
      (item) => item is ListItemWord && item.word.id == wordId,
    );

    if (itemIndex == -1) return this;

    updatedItems.removeAt(itemIndex);

    // Handle language header removal if needed
    if (itemIndex > 0 &&
        itemIndex - 1 < updatedItems.length &&
        updatedItems[itemIndex - 1] is ListItemHeader) {
      final isLastWordForLanguage = itemIndex == updatedItems.length ||
          (itemIndex < updatedItems.length &&
              updatedItems[itemIndex] is ListItemHeader);

      if (isLastWordForLanguage) {
        updatedItems.removeAt(itemIndex - 1);
      }
    }

    return copyWith(groupedBookmarkItems: updatedItems);
  }
}
