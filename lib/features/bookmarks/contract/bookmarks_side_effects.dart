import 'package:freezed_annotation/freezed_annotation.dart';

part 'bookmarks_side_effects.freezed.dart';

@freezed
sealed class BookmarksSideEffect with _$BookmarksSideEffect {
  const factory BookmarksSideEffect.showMessage(String message) =
      ShowMessageSideEffect;
  const factory BookmarksSideEffect.goBack() = GoBackSideEffect;
  const factory BookmarksSideEffect.navigateToWordDetail(String wordId) =
      NavigateToWordDetailSideEffect;
  const factory BookmarksSideEffect.navigateToPremium(bool isGuest) =
      NavigateToPremiumSideEffect;
}
