import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import 'contract/word_detail_contract.dart';
import 'word_detail_presenter.dart';

class WordDetailScreen extends ConsumerWidget {
  final String wordId;

  const WordDetailScreen({
    super.key,
    required this.wordId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state =
        ref.watch(wordDetailPresenterProvider(wordId: wordId, key: key!));
    final presenter = ref
        .read(wordDetailPresenterProvider(wordId: wordId, key: key!).notifier);
    _handleSideEffects(context, ref);

    return AdMobScaffold(
      backgroundColor: Palette.surface,
      hasBottomSheetBackground: true,
      body: state.loadingState == ScreenLoadingState.loaded
          ? _buildWordDetailContent(context, state, presenter)
          : ScreenLoading(
              hasBottomSheetBackground: true,
              state: state.loadingState,
              onRetry: () => presenter.intentHandler(
                const WordDetailIntent.retry(),
              ),
            ),
    );
  }

  Widget _buildWordDetailContent(BuildContext context, WordDetailState state,
      WordDetailPresenter presenter) {
    final word = state.word!;
    final str = Str.of(context);

    return Column(
      children: [
        _buildHeader(presenter),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  word.language.name,
                  style: TextStyles.labelLarge.copyWith(
                    color: Palette.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                _buildWordRow(word, presenter, state),
                if (word.phonetic != null) ...[
                  const SizedBox(height: 2),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      InkWell(
                        onTap: () => presenter.intentHandler(
                            WordDetailIntent.playPronunciation(word)),
                        borderRadius: BorderRadius.circular(4),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return Row(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  ConstrainedBox(
                                    constraints: BoxConstraints(
                                      maxWidth:
                                          MediaQuery.of(context).size.width -
                                              68,
                                    ),
                                    child: Container(
                                      padding: EdgeInsets.zero,
                                      margin: EdgeInsets.zero,
                                      child: Text(
                                        word.phonetic!,
                                        style: TextStyles.bodyMedium.copyWith(
                                          color: Palette.onSurfaceVariant,
                                          fontStyle: FontStyle.italic,
                                          fontSize: 14,
                                        ),
                                        softWrap: true,
                                        textAlign: TextAlign.start,
                                        maxLines: 100,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Icon(
                                    Icons.volume_up_outlined,
                                    size: 20,
                                    color: Palette.purpule4f,
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  )
                ],
                const SizedBox(height: 8),
                _buildSpicinessRow(word),
                const SizedBox(height: 24),
                Text(
                  str.wordDetailMeaning,
                  style: TextStyles.labelLarge.copyWith(
                    color: Palette.black1d,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  word.meaning,
                  style: TextStyles.bodyLarge.copyWith(
                    color: Palette.onSurface,
                  ),
                ),
                const SizedBox(height: 20),
                const Divider(color: Palette.outlineVariant),
                const SizedBox(height: 20),
                Text(
                  str.wordDetailUsage,
                  style: TextStyles.labelLarge.copyWith(
                    color: Palette.black1d,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                _buildUsagesList(word),
                const SizedBox(height: 12),
                const Divider(color: Palette.outlineVariant),
                const SizedBox(height: 20),
                InkWell(
                  onTap: () => presenter.intentHandler(
                    const WordDetailIntent.suggestEdit(),
                  ),
                  child: TextWithHighlight(
                    text: Str.of(context).wordDetailSuggestEdit,
                    highlightedText:
                        Str.of(context).wordDetailSuggestEditHighlight,
                    style: TextStyles.titleMedium
                        .copyWith(color: Palette.onSurface),
                    highlightStyle:
                        TextStyles.titleMedium.copyWith(color: Palette.primary),
                  ),
                ),
                const SizedBox(height: 4),
                InkWell(
                  onTap: () => presenter.intentHandler(
                    const WordDetailIntent.reportInaccuracy(),
                  ),
                  child: TextWithHighlight(
                    text: Str.of(context).wordDetailReportInaccuracy,
                    highlightedText:
                        Str.of(context).wordDetailReportInaccuracyHighlight,
                    style: TextStyles.titleMedium
                        .copyWith(color: Palette.onSurface),
                    highlightStyle:
                        TextStyles.titleMedium.copyWith(color: Palette.primary),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(WordDetailPresenter presenter) => Container(
        padding: const EdgeInsets.fromLTRB(24, 12, 12, 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Text(
                Str.current.wordDetailMeaningAndUsage,
                style: TextStyles.titleLarge.copyWith(
                  color: Palette.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.close),
              color: Palette.onSurface,
              onPressed: () => presenter.intentHandler(
                const WordDetailIntent.close(),
              ),
            ),
          ],
        ),
      );

  Widget _buildWordRow(
      WordEntity word, WordDetailPresenter presenter, WordDetailState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Container(
            padding: EdgeInsets.zero,
            margin: EdgeInsets.zero,
            child: Text(
              word.word,
              style: TextStyles.displaySmall.copyWith(
                color: Palette.primary,
                fontWeight: FontWeight.w600,
                height: 1,
              ),
              softWrap: true,
              textAlign: TextAlign.start,
              maxLines: 100,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        SizedBox(
          width: 36,
          height: 36,
          child: IconButton(
            padding: const EdgeInsets.only(top: 2),
            constraints: const BoxConstraints(),
            icon: Icon(
                word.isBookmarked ? Icons.bookmark : Icons.bookmark_border),
            color: Palette.primary,
            onPressed: () => presenter.intentHandler(
              const WordDetailIntent.toggleBookmark(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSpicinessRow(WordEntity word) {
    return Row(
      children: [
        SvgPicture.asset(
          word.spiciness.getIconPath(),
          width: 24,
          height: 24,
          colorFilter: const ColorFilter.mode(
            Palette.primary,
            BlendMode.srcIn,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          word.spiciness.getTitle(),
          style: TextStyles.labelLarge.copyWith(
            color: Palette.primary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildUsagesList(WordEntity word) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: word.usages.asMap().entries.map((entry) {
        final index = entry.key + 1;
        final usage = entry.value;
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 20,
                child: Text(
                  '$index.',
                  style: TextStyles.titleMedium.copyWith(
                    color: Palette.onSurface,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  usage,
                  style: TextStyles.titleMedium.copyWith(
                    color: Palette.onSurface,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(wordDetailSideEffectsProvider(wordId: wordId, key: key!),
        (_, next) {
      final presenter = ref.read(
          wordDetailPresenterProvider(wordId: wordId, key: key!).notifier);
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case ShowMessageSideEffect _:
            Toast.show(context, sideEffect.message);
            break;
          case ShowMessageWithActionSideEffect _:
            Toast.showWithAction(
              context,
              message: sideEffect.message,
              actionLabel: sideEffect.actionText,
              onActionPressed: () => presenter
                  .intentHandler(const WordDetailIntent.navigateToBookmarks()),
            );
            break;
          case CloseSideEffect _:
            Navigator.of(context).pop();
            break;
          case final NavigateToEditSuggestionSideEffect se:
            GoRouter.of(context).pushToEditSuggestion(word: se.word);
            break;
          case final NavigateToReportSideEffect se:
            GoRouter.of(context).pushToReport(word: se.word);
            break;
          case NavigateToSignInSideEffect _:
            GoRouter.of(context).pushToSignIn(hasCustomDest: true);
            break;
          case NavigateToBookmarksSideEffect _:
            GoRouter.of(context).pushToBookmarks();
            break;
          case final NavigateToPremiumSideEffect se:
            GoRouter.of(context).pushToPremium(se.isGuest);
            break;
        }
      });
    });
  }
}
