import 'package:cussme/domain/entities/word_entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_detail_intent.freezed.dart';

@freezed
sealed class WordDetailIntent with _$WordDetailIntent {
  const factory WordDetailIntent.retry() = RetryIntent;
  const factory WordDetailIntent.close() = CloseIntent;
  const factory WordDetailIntent.toggleBookmark() = ToggleBookmarkIntent;
  const factory WordDetailIntent.playPronunciation(WordEntity word) =
      PlayPronunciationIntent;
  const factory WordDetailIntent.suggestEdit() = SuggestEditIntent;
  const factory WordDetailIntent.reportInaccuracy() = ReportInaccuracyIntent;
  const factory WordDetailIntent.navigateToBookmarks() =
      NavigateToBookmarksIntent;
}
