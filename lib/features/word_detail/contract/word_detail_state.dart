import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_detail_state.freezed.dart';

@freezed
sealed class WordDetailState with _$WordDetailState {
  const factory WordDetailState({
    @Default(null) WordEntity? word,
    @Default(ScreenLoadingState.loading) ScreenLoadingState loadingState,
    @Default(false) bool isGuest,
  }) = _WordDetailState;
}
