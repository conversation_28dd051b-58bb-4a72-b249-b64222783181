import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_suggestion_side_effects.freezed.dart';

@freezed
sealed class EditSuggestionSideEffect with _$EditSuggestionSideEffect {
  const factory EditSuggestionSideEffect.close() = CloseSideEffect;
  const factory EditSuggestionSideEffect.showMessage(String message) = ShowMessageSideEffect;
  const factory EditSuggestionSideEffect.showSuccessDialog() = ShowSuccessDialogSideEffect;
}
