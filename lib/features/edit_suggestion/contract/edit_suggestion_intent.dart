import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_suggestion_intent.freezed.dart';

@freezed
sealed class EditSuggestionIntent with _$EditSuggestionIntent {
  const factory EditSuggestionIntent.close() = CloseIntent;
  const factory EditSuggestionIntent.suggestionChanged(String suggestion) = SuggestionChangedIntent;
  const factory EditSuggestionIntent.spicinessSelected(Spiciness spiciness) = SpicinessSelectedIntent;
  const factory EditSuggestionIntent.sendSuggestion() = SendSuggestionIntent;
}
