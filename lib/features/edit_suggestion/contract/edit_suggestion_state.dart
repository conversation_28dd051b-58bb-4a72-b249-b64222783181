import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_suggestion_state.freezed.dart';

@freezed
sealed class EditSuggestionState with _$EditSuggestionState {
  const factory EditSuggestionState({
    required WordEntity word,
    @Default('') String suggestion,
    @Default(null) Spiciness? selectedSpiciness,
    @Default(false) bool isLoading,
    @Default(false) bool isSendEnabled,
  }) = _EditSuggestionState;
}
