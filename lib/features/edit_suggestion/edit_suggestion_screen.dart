import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'contract/edit_suggestion_contract.dart';
import 'edit_suggestion_presenter.dart';

class EditSuggestionScreen extends ConsumerWidget {
  final WordEntity word;

  const EditSuggestionScreen({
    super.key,
    required this.word,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(editSuggestionPresenterProvider(word: word));
    final presenter =
        ref.read(editSuggestionPresenterProvider(word: word).notifier);
    _handleSideEffects(context, ref);

    return AdMobScaffold(
      backgroundColor: Palette.surface,
      hasBottomSheetBackground: true,
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.fromLTRB(4, 16, 12, 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  color: Palette.onSurface,
                  onPressed: () => presenter.intentHandler(
                    const EditSuggestionIntent.close(),
                  ),
                ),
                Expanded(
                  child: Text(
                    Str.of(context).editSuggestionTitle,
                    style: TextStyles.titleLarge.copyWith(
                      color: Palette.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        state.word.language.name,
                        style: TextStyles.labelMedium.copyWith(
                          color: Palette.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        state.word.word,
                        style: TextStyles.headlineMedium.copyWith(
                          color: Palette.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Divider(color: Palette.outlineVariant, height: 1),
                  const SizedBox(height: 12),
                  _buildSpicinessSelection(context, state, presenter),
                  const SizedBox(height: 20),
                  TextField(
                    maxLines: 5,
                    decoration: InputDecoration(
                      labelText: Str.of(context).editSuggestionFieldLabel,
                      border: const OutlineInputBorder(),
                      alignLabelWithHint: true,
                      floatingLabelAlignment: FloatingLabelAlignment.start,
                    ),
                    onChanged: (value) {
                      presenter.intentHandler(
                        EditSuggestionIntent.suggestionChanged(value),
                      );
                    },
                  ),
                  const SizedBox(height: 24),
                  Align(
                    alignment: Alignment.centerRight,
                    child: MinSizedTextButton(
                      text: Str.of(context).editSuggestionSendButton,
                      onPressed: () => presenter.intentHandler(
                        const EditSuggestionIntent.sendSuggestion(),
                      ),
                      minSize: const Size(120, 40),
                      isEnabled: state.isSendEnabled,
                      isLoading: state.isLoading,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 10),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpicinessSelection(
    BuildContext context,
    EditSuggestionState state,
    EditSuggestionPresenter presenter,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Str.of(context).editSuggestionChooseSpiciness,
          style: TextStyles.labelMedium.copyWith(
            color: Palette.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildSpicinessItem(Spiciness.mild,
                state.selectedSpiciness ?? state.word.spiciness, presenter),
            _buildSpicinessItem(Spiciness.cheeky,
                state.selectedSpiciness ?? state.word.spiciness, presenter),
            _buildSpicinessItem(Spiciness.spicy,
                state.selectedSpiciness ?? state.word.spiciness, presenter),
            _buildSpicinessItem(Spiciness.slang,
                state.selectedSpiciness ?? state.word.spiciness, presenter),
          ],
        ),
      ],
    );
  }

  Widget _buildSpicinessItem(
    Spiciness spiciness,
    Spiciness currentSpiciness,
    EditSuggestionPresenter presenter,
  ) {
    final isSelected = spiciness == currentSpiciness;
    return InkWell(
      onTap: () => presenter.intentHandler(
        EditSuggestionIntent.spicinessSelected(spiciness),
      ),
      child: Column(
        children: [
          SvgPicture.asset(
            spiciness.getIconPath(),
            width: 36,
            height: 36,
            colorFilter: ColorFilter.mode(
              isSelected
                  ? Palette.primary
                  : Palette.black59.withValues(alpha: 0.2),
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            spiciness.getTitle(),
            style: TextStyles.bodyMedium.copyWith(color: Palette.onSurface),
          ),
        ],
      ),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(editSuggestionSideEffectsProvider(word: word), (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case CloseSideEffect _:
            Navigator.of(context).pop();
            break;
          case ShowMessageSideEffect _:
            Toast.show(context, sideEffect.message);
            break;
          case ShowSuccessDialogSideEffect _:
            SuccessDialog.show(context, Str.of(context).suggestionSentTitle);
            break;
        }
      });
    });
  }
}
