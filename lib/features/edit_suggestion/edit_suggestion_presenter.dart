import 'dart:async';

import 'package:cussme/data/use_cases/word_use_case.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/edit_suggestion_contract.dart';

part 'edit_suggestion_presenter.g.dart';

@riverpod
class EditSuggestionPresenter extends _$EditSuggestionPresenter {
  late StreamController<EditSuggestionSideEffect> sideEffects =
      StreamController<EditSuggestionSideEffect>();

  late final WordUseCase _wordUseCase;

  @override
  EditSuggestionState build({required WordEntity word}) {
    _wordUseCase = ref.read(wordUseCaseProvider);

    ref.onDispose(() => sideEffects.close());

    state = EditSuggestionState(
      word: word,
      selectedSpiciness: word.spiciness,
    );

    return state;
  }

  void intentHandler(EditSuggestionIntent intent) {
    switch (intent) {
      case CloseIntent _:
        sideEffects.safeAdd(const EditSuggestionSideEffect.close());
        break;
      case final SuggestionChangedIntent intent:
        state = state.copyWith(suggestion: intent.suggestion);
        _updateSendButtonState();
        break;
      case final SpicinessSelectedIntent intent:
        state = state.copyWith(selectedSpiciness: intent.spiciness);
        _updateSendButtonState();
        break;
      case SendSuggestionIntent _:
        _submitSuggestion();
        break;
    }
  }

  void _updateSendButtonState() {
    final hasSuggestionText = state.suggestion.trim().isNotEmpty;
    final hasSpicinessChanged = state.selectedSpiciness != state.word.spiciness;

    state = state.copyWith(
      isSendEnabled: hasSuggestionText || hasSpicinessChanged,
    );
  }

  Future<void> _submitSuggestion() async {
    if (!state.isSendEnabled) return;

    try {
      state = state.copyWith(isLoading: true);

      await _wordUseCase.submitWordSuggestion(
        wordId: state.word.id,
        suggestion: state.suggestion,
        spiciness: state.selectedSpiciness ?? state.word.spiciness,
      );

      sideEffects.safeAdd(const EditSuggestionSideEffect.showSuccessDialog());
    } on CussMeException catch (e) {
      sideEffects.safeAdd(EditSuggestionSideEffect.showMessage(e.message));
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}

@riverpod
Stream<EditSuggestionSideEffect> editSuggestionSideEffects(
  Ref ref, {
  required WordEntity word,
}) {
  return ref
      .watch(editSuggestionPresenterProvider(word: word).notifier)
      .sideEffects
      .stream;
}
