import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../localization/generated/l10n.dart';
import '../../../routing/app_router.dart';
import 'contract/forgot_password_contract.dart';
import 'forgot_password_presenter.dart';

class ForgotPasswordScreen extends ConsumerWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final state = ref.watch(forgotPasswordPresenterProvider);
    final presenter = ref.read(forgotPasswordPresenterProvider.notifier);
    final str = Str.of(context);

    return AdMobScaffold(
      backgroundColor: Palette.whiteFa,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        toolbarHeight: 56 + 12, // Default height + top padding
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Padding(
          padding: const EdgeInsets.fromLTRB(8.0, 12.0, 8.0, 0.0),
          child: AppBar(
            backgroundColor: Colors.transparent,
            title: Text(
              str.forgotPasswordTitle,
              style: TextStyles.titleLarge.copyWith(
                color: Palette.onSurface,
              ),
            ),
            automaticallyImplyLeading: false,
            actions: [
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => presenter.intentHandler(
                  const ForgotPasswordIntent.back(),
                ),
              ),
            ],
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                str.sendResetLink,
                style: TextStyles.titleMedium.copyWith(
                  color: Palette.onSurface,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: Text(
                str.forgotPasswordDescription,
                style: TextStyles.bodyMedium.copyWith(
                  color: Palette.onSurfaceVariant,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            CustomTextField(
              label: str.emailLabel,
              errorText: state.emailError,
              onChanged: (value) => presenter.intentHandler(
                ForgotPasswordIntent.emailChanged(value),
              ),
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.done,
            ),
            const SizedBox(height: 24),
            PrimaryButton(
              text: str.sendResetLink,
              onPressed: () => presenter.intentHandler(
                const ForgotPasswordIntent.sendResetLink(),
              ),
              isLoading: state.isLoading,
            ),
          ],
        ),
      ),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(forgotPasswordSideEffectsProvider, (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateBackSideEffect _:
            Navigator.of(context).pop();
            break;
          case final NavigateToResetPasswordSentSideEffect intent:
            GoRouter.of(context)
                .pushReplaceToResetPasswordSent(email: intent.email);
            break;
          case final ShowErrorSideEffect intent:
            Toast.show(context, intent.message);
            break;
          case HideKeyboardSideEffect _:
            KeyboardUtils.hideKeyboard(context);
            break;
        }
      });
    });
  }
}
