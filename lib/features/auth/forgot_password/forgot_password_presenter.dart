import 'dart:async';

import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/use_cases/auth_use_case.dart';
import '../../../domain/domain.dart';
import 'contract/forgot_password_contract.dart';

part 'forgot_password_presenter.g.dart';

@riverpod
class ForgotPasswordPresenter extends _$ForgotPasswordPresenter {
  final StreamController<ForgotPasswordSideEffect> sideEffects =
      StreamController<ForgotPasswordSideEffect>();
  late final AuthUseCase _authUseCase;

  @override
  ForgotPasswordState build() {
    ref.onDispose(() => sideEffects.close());
    _authUseCase = ref.read(authUseCaseProvider);
    return const ForgotPasswordState();
  }

  void intentHandler(ForgotPasswordIntent intent) {
    switch (intent) {
      case final EmailChangedIntent intent:
        state = state.copyWith(email: intent.email, emailError: null);
        break;
      case SendResetLinkIntent _:
        _hideKeyboard();
        _sendResetLink();
        break;
      case BackIntent _:
        _hideKeyboard();
        sideEffects.safeAdd(const ForgotPasswordSideEffect.navigateBack());
        break;
    }
  }

  void _hideKeyboard() {
    sideEffects.safeAdd(const ForgotPasswordSideEffect.hideKeyboard());
  }

  bool _validateEmail() {
    state = state.copyWith(emailError: null);

    final emailError = Validators.validateEmail(state.email);
    if (emailError != null) {
      state = state.copyWith(emailError: emailError);
      return false;
    }

    return true;
  }

  Future<void> _sendResetLink() async {
    if (!_validateEmail()) return;

    try {
      state = state.copyWith(isLoading: true);

      await _authUseCase.sendPasswordResetEmail(state.email.trim());

      sideEffects.safeAdd(ForgotPasswordSideEffect.navigateToResetPasswordSent(
          state.email.trim()));
    } on CussMeException catch (e) {
      sideEffects.safeAdd(ForgotPasswordSideEffect.showError(e.message));
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}

@riverpod
Stream<ForgotPasswordSideEffect> forgotPasswordSideEffects(Ref ref) {
  return ref.read(forgotPasswordPresenterProvider.notifier).sideEffects.stream;
}
