import 'package:freezed_annotation/freezed_annotation.dart';

part 'forgot_password_side_effects.freezed.dart';

@freezed
sealed class ForgotPasswordSideEffect with _$ForgotPasswordSideEffect {
  const factory ForgotPasswordSideEffect.navigateBack() =
      NavigateBackSideEffect;
  const factory ForgotPasswordSideEffect.navigateToResetPasswordSent(
      String email) = NavigateToResetPasswordSentSideEffect;
  const factory ForgotPasswordSideEffect.showError(String message) =
      ShowErrorSideEffect;
  const factory ForgotPasswordSideEffect.hideKeyboard() =
      HideKeyboardSideEffect;
}
