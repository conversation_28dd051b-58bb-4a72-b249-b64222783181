import 'package:freezed_annotation/freezed_annotation.dart';

part 'forgot_password_intent.freezed.dart';

@freezed
sealed class ForgotPasswordIntent with _$ForgotPasswordIntent {
  const factory ForgotPasswordIntent.emailChanged(String email) =
      EmailChangedIntent;
  const factory ForgotPasswordIntent.sendResetLink() = SendResetLinkIntent;
  const factory ForgotPasswordIntent.back() = BackIntent;
}
