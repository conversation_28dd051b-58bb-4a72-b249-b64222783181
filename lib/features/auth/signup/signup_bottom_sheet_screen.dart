import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'widgets/signup_form.dart';

class SignUpBottomSheetContent extends ConsumerWidget {
  final NavigationData? navigationData;
  final VoidCallback onSwitchToSignIn;
  final ScrollController scrollController;

  const SignUpBottomSheetContent({
    super.key,
    this.navigationData,
    required this.onSwitchToSignIn,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) => AdMobScaffold(
        body: SignUpForm(
          scrollController: scrollController,
          navigationData: navigationData,
          onSwitchToSignIn: onSwitchToSignIn,
          isBottomSheet: true,
        ),
      );
}
