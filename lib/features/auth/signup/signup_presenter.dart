import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../localization/generated/l10n.dart';
import '../../../utils/utils.dart';
import 'contract/signup_contract.dart';

part 'signup_presenter.g.dart';

@riverpod
class SignUpPresenter extends _$SignUpPresenter {
  StreamController<SignUpSideEffect> sideEffects =
      StreamController<SignUpSideEffect>();
  late final AuthUseCase _authUseCase;

  @override
  SignUpState build({NavigationData? navigationData}) {
    _authUseCase = ref.read(authUseCaseProvider);

    ref.onDispose(() => sideEffects.close());
    return SignUpState(navigationData: navigationData);
  }

  void _hideKeyboard() {
    sideEffects.safeAdd(const SignUpSideEffect.hideKeyboard());
  }

  void _navigateAfterAuth(UserEntity user) {
    if (state.navigationData != null) {
      sideEffects.safeAdd(SignUpSideEffect.navigateToDestination(
        state.navigationData!.copyWith(user: user),
      ));
    } else {
      sideEffects.safeAdd(const SignUpSideEffect.navigateToHome());
    }
  }

  void intentHandler(SignUpIntent intent) {
    switch (intent) {
      case final FirstNameChangedIntent intent:
        state =
            state.copyWith(firstName: intent.firstName, firstNameError: null);
        break;
      case final LastNameChangedIntent intent:
        state = state.copyWith(lastName: intent.lastName, lastNameError: null);
        break;
      case final EmailChangedIntent intent:
        state = state.copyWith(email: intent.email, emailError: null);
        break;
      case final PasswordChangedIntent intent:
        state = state.copyWith(password: intent.password, passwordError: null);
        break;
      case final ConfirmPasswordChangedIntent intent:
        state = state.copyWith(
            confirmPassword: intent.confirmPassword,
            confirmPasswordError: null);
        break;
      case TogglePasswordVisibilityIntent _:
        state = state.copyWith(isPasswordVisible: !state.isPasswordVisible);
        break;
      case ToggleConfirmPasswordVisibilityIntent _:
        state = state.copyWith(
            isConfirmPasswordVisible: !state.isConfirmPasswordVisible);
        break;
      case SignUpWithEmailIntent _:
        _hideKeyboard();
        _signUpWithEmail();
        break;
      case SignUpWithGoogleIntent _:
        _hideKeyboard();
        _signUpWithGoogle();
        break;
      case SignUpWithAppleIntent _:
        _hideKeyboard();
        _signUpWithApple();
        break;
      case NavigateToSignInIntent _:
        _hideKeyboard();
        sideEffects.safeAdd(const SignUpSideEffect.navigateToSignIn());
        break;
      case ContinueWithoutSignUpIntent _:
        _hideKeyboard();
        _continueAsGuest();
        break;
      case OpenPrivacyPolicyIntent _:
        sideEffects.safeAdd(const SignUpSideEffect.openUrl(privacyPolicyUrl));
        break;
      case OpenTermsAndConditionsIntent _:
        sideEffects
            .safeAdd(const SignUpSideEffect.openUrl(termsAndConditionsUrl));
        break;
      case OpenEulaIntent _:
        sideEffects.safeAdd(const SignUpSideEffect.openUrl(eulaUrl));
        break;
    }
  }

  bool _validateForm() {
    bool isValid = true;

    state = state.copyWith(
      firstNameError: null,
      emailError: null,
      passwordError: null,
      confirmPasswordError: null,
    );

    if (state.firstName.isEmpty) {
      state = state.copyWith(firstNameError: Str.current.firstNameRequired);
      isValid = false;
    }

    final emailError = Validators.validateEmail(state.email);
    if (emailError != null) {
      state = state.copyWith(emailError: emailError);
      isValid = false;
    }

    final passwordError = Validators.validatePassword(state.password);
    if (passwordError != null) {
      state = state.copyWith(passwordError: passwordError);
      isValid = false;
    }

    final confirmPasswordError = Validators.validateConfirmPassword(
      state.password,
      state.confirmPassword,
    );
    if (confirmPasswordError != null) {
      state = state.copyWith(confirmPasswordError: confirmPasswordError);
      isValid = false;
    }

    if (!isValid) {
      sideEffects.safeAdd(
          SignUpSideEffect.showError(Str.current.invalidInputsErrorMessage));
    }

    return isValid;
  }

  Future<void> _signUpWithEmail() async {
    if (!_validateForm()) return;

    try {
      state = state.copyWith(isEmailLoading: true, emailAlreadyExists: false);

      final user = await _authUseCase.signUpWithEmail(
        email: state.email.trim(),
        password: state.password,
        firstname: state.firstName.trim(),
        lastname: state.lastName.trim().isEmpty ? null : state.lastName.trim(),
      );

      if (state.navigationData != null) {
        _navigateAfterAuth(user);
      } else {
        sideEffects.safeAdd(const SignUpSideEffect.navigateToIntro());
      }
    } on CussMeException catch (e) {
      state = state.copyWith(isEmailLoading: false);
      if (e is EmailAlreadyExistsException) {
        state = state.copyWith(emailAlreadyExists: true);
      } else {
        sideEffects.safeAdd(SignUpSideEffect.showError(e.message));
      }
    }
  }

  Future<void> _signUpWithGoogle() async {
    try {
      state = state.copyWith(isGoogleLoading: true);

      final (user, profileExists) = await _authUseCase.signInWithGoogle();

      if (profileExists || state.navigationData != null) {
        _navigateAfterAuth(user);
      } else {
        sideEffects.safeAdd(const SignUpSideEffect.navigateToIntro());
      }
    } on CussMeException catch (e) {
      state = state.copyWith(isGoogleLoading: false);
      sideEffects.safeAdd(SignUpSideEffect.showError(e.message));
    }
  }

  Future<void> _signUpWithApple() async {
    try {
      state = state.copyWith(isAppleLoading: true);

      final (user, profileExists) = await _authUseCase.signInWithApple();

      if (profileExists || state.navigationData != null) {
        _navigateAfterAuth(user);
      } else {
        sideEffects.safeAdd(const SignUpSideEffect.navigateToIntro());
      }
    } on CussMeException catch (e) {
      state = state.copyWith(isAppleLoading: false);
      sideEffects.safeAdd(SignUpSideEffect.showError(e.message));
    }
  }

  Future<void> _continueAsGuest() async {
    try {
      await _authUseCase.setGuest();

      sideEffects.safeAdd(const SignUpSideEffect.navigateToGuestHome());
    } on CussMeException catch (e) {
      sideEffects.safeAdd(SignUpSideEffect.showError(e.message));
    }
  }
}

@riverpod
Stream<SignUpSideEffect> signUpSideEffects(Ref ref,
    {NavigationData? navigationData}) {
  return ref
      .read(signUpPresenterProvider(navigationData: navigationData).notifier)
      .sideEffects
      .stream;
}
