import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'widgets/signup_form.dart';

class SignUpScreen extends ConsumerWidget {
  final NavigationData? navigationData;

  const SignUpScreen({super.key, this.navigationData});

  @override
  Widget build(BuildContext context, WidgetRef ref) => AdMobScaffold(
        body: SignUpForm(navigationData: navigationData),
      );
}
