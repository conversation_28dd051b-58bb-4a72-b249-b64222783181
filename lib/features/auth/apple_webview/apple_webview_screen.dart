import 'package:cussme/data/data.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'apple_webview_presenter.dart';
import 'contract/apple_webview_contract.dart';

class AppleWebViewScreen extends ConsumerStatefulWidget {
  final NavigationData? navigationData;

  const AppleWebViewScreen({super.key, this.navigationData});

  @override
  ConsumerState<AppleWebViewScreen> createState() => _AppleWebViewScreenState();
}

class _AppleWebViewScreenState extends ConsumerState<AppleWebViewScreen> {
  late WebViewController _webViewController;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    WidgetsBinding.instance.addPostFrameCallback((_) => _loadInitialUrl());
  }

  void _initializeWebView() {
    final presenter = ref.read(
      appleWebViewPresenterProvider(navigationData: widget.navigationData)
          .notifier,
    );
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..runJavaScriptReturningResult("window.location.href").then((value) {
        final url = value.toString().replaceAll('"', '');
        if (url.startsWith(appleSignInRedirectUrl)) {
          final uriWithCode = Uri.parse(url.replaceFirst('#', '?'));
          final code = uriWithCode.queryParameters['code'];
          if (code != null) {
            // ✅ Proceed to exchange this code for tokens
            print('✅ Got Apple code: $code');
          } else {
            print('❌ Code not found in fragment');
          }
        }
      })
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            final presenter = ref.read(
              appleWebViewPresenterProvider(
                      navigationData: widget.navigationData)
                  .notifier,
            );
            presenter.intentHandler(AppleWebViewIntent.urlChanged(url));
          },
          onNavigationRequest: (NavigationRequest request) {
            // Check if this is the Apple redirect URL
            if (request.url.startsWith(appleSignInRedirectUrl)) {
              // Prevent navigation and handle it manually
              final presenter = ref.read(
                appleWebViewPresenterProvider(
                        navigationData: widget.navigationData)
                    .notifier,
              );
              presenter
                  .intentHandler(AppleWebViewIntent.urlChanged(request.url));
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onPageFinished: (String url) {
            final presenter = ref.read(
              appleWebViewPresenterProvider(
                      navigationData: widget.navigationData)
                  .notifier,
            );
            presenter.intentHandler(AppleWebViewIntent.urlChanged(url));
          },
        ),
      );
  }

  void _loadInitialUrl() {
    final presenter = ref.read(
      appleWebViewPresenterProvider(navigationData: widget.navigationData)
          .notifier,
    );
    _webViewController.loadRequest(Uri.parse(presenter.getAppleSignInUrl()));
  }

  @override
  Widget build(BuildContext context) {
    _handleSideEffects(context);

    return WebViewWidget(controller: _webViewController);
  }

  void _handleSideEffects(BuildContext context) {
    ref.listen(
      appleWebViewSideEffectsProvider(navigationData: widget.navigationData),
      (_, next) {
        next.whenData((sideEffect) {
          switch (sideEffect) {
            case GoBackSideEffect _:
              context.pop();
              break;
            case final ShowErrorSideEffect se:
              Toast.show(context, se.message);
              break;
            case final LaunchUrlSideEffect se:
              openInBrowser(se.url);
              break;
            case NavigateToHomeSideEffect _:
              GoRouter.of(context).goToHome();
              break;
            case NavigateToGuestHomeSideEffect _:
              GoRouter.of(context).goToGuestHome();
              break;
            case NavigateToIntroSideEffect _:
              GoRouter.of(context)
                  .goToIntro(navigationData: widget.navigationData);
              break;
            case final NavigateToDestinationSideEffect se:
              GoRouter.of(context)
                  .goToDestinationWithHomeInStack(se.navigationData);
              break;
          }
        });
      },
    );
  }
}
