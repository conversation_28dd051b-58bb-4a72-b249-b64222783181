import 'package:cussme/routing/navigation_data.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'apple_webview_state.freezed.dart';

@freezed
sealed class AppleWebViewState with _$AppleWebViewState {
  const factory AppleWebViewState({
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    @Default('') String currentUrl,
    @Default(null) String? errorMessage,
    @Default(null) NavigationData? navigationData,
  }) = _AppleWebViewState;
}
