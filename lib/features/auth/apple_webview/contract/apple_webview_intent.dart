import 'package:freezed_annotation/freezed_annotation.dart';

part 'apple_webview_intent.freezed.dart';

@freezed
sealed class AppleWebViewIntent with _$AppleWebViewIntent {
  const factory AppleWebViewIntent.loadAppleSignIn() = LoadAppleSignInIntent;
  const factory AppleWebViewIntent.urlChanged(String url) = UrlChangedIntent;
  const factory AppleWebViewIntent.goBack() = GoBackIntent;
  const factory AppleWebViewIntent.retry() = RetryIntent;
}
