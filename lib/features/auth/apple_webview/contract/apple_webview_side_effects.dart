import 'package:cussme/routing/navigation_data.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'apple_webview_side_effects.freezed.dart';

@freezed
sealed class AppleWebViewSideEffect with _$AppleWebViewSideEffect {
  const factory AppleWebViewSideEffect.goBack() = GoBackSideEffect;
  const factory AppleWebViewSideEffect.showError(String message) = ShowErrorSideEffect;
  const factory AppleWebViewSideEffect.launchUrl(String url) = LaunchUrlSideEffect;
  const factory AppleWebViewSideEffect.navigateToHome() = NavigateToHomeSideEffect;
  const factory AppleWebViewSideEffect.navigateToGuestHome() = NavigateToGuestHomeSideEffect;
  const factory AppleWebViewSideEffect.navigateToIntro() = NavigateToIntroSideEffect;
  const factory AppleWebViewSideEffect.navigateToDestination(NavigationData navigationData) = NavigateToDestinationSideEffect;
}
