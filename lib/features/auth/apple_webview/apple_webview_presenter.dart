import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/apple_webview_contract.dart';

part 'apple_webview_presenter.g.dart';

@riverpod
class AppleWebViewPresenter extends _$AppleWebViewPresenter {
  late final StreamController<AppleWebViewSideEffect> _sideEffectsController;

  Stream<AppleWebViewSideEffect> get sideEffects =>
      _sideEffectsController.stream;

  @override
  AppleWebViewState build({NavigationData? navigationData}) {
    _sideEffectsController =
        StreamController<AppleWebViewSideEffect>.broadcast();

    ref.onDispose(() {
      _sideEffectsController.close();
    });

    return AppleWebViewState(navigationData: navigationData);
  }

  void intentHandler(AppleWebViewIntent intent) {
    switch (intent) {
      case LoadAppleSignInIntent _:
        _loadAppleSignIn();
        break;
      case final UrlChangedIntent intent:
        _handleUrlChanged(intent.url);
        break;
      case GoBackIntent _:
        _sideEffectsController.safeAdd(const AppleWebViewSideEffect.goBack());
        break;
      case RetryIntent _:
        _loadAppleSignIn();
        break;
    }
  }

  void _loadAppleSignIn() {
    state =
        state.copyWith(isLoading: true, hasError: false, errorMessage: null);
  }

  void _handleUrlChanged(String url) {
    state = state.copyWith(currentUrl: url);

    // Check if we've reached the Apple redirect URL
    if (url.startsWith(appleSignInRedirectUrl)) {
      // Launch the URL using Flutter's URL launcher to complete the authentication
      _sideEffectsController.safeAdd(AppleWebViewSideEffect.launchUrl(url));
      _handleAppleAuthenticationComplete(url);
    }
  }

  Future<void> _handleAppleAuthenticationComplete(String redirectUrl) async {
    try {
      state = state.copyWith(isLoading: true);

      // After launching the URL, the Apple authentication will be handled by the system
      // We'll close this screen and let the normal Apple Sign In flow continue
      _sideEffectsController.safeAdd(const AppleWebViewSideEffect.goBack());
    } on CussMeException catch (e) {
      state = state.copyWith(
          isLoading: false, hasError: true, errorMessage: e.message);
      _sideEffectsController
          .safeAdd(AppleWebViewSideEffect.showError(e.message));
    }
  }

  String getAppleSignInUrl() {
    // Construct the Apple Sign In URL manually
    const clientId = 'xyz.cussme.auth';
    final redirectUri = Uri.encodeComponent(appleSignInRedirectUrl);
    // const scope = 'name email';
    const responseType = 'code id_token';
    const responseMode = 'fragment';

    return 'https://appleid.apple.com/auth/authorize'
        '?client_id=$clientId'
        '&redirect_uri=$redirectUri'
        // '&scope=$scope'
        '&response_type=$responseType'
        '&response_mode=$responseMode';
  }
}

@riverpod
Stream<AppleWebViewSideEffect> appleWebViewSideEffects(Ref ref,
    {NavigationData? navigationData}) {
  return ref
      .read(appleWebViewPresenterProvider(navigationData: navigationData)
          .notifier)
      .sideEffects;
}
