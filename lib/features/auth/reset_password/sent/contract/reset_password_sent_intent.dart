import 'package:freezed_annotation/freezed_annotation.dart';

part 'reset_password_sent_intent.freezed.dart';

@freezed
sealed class ResetPasswordSentIntent with _$ResetPasswordSentIntent {
  const factory ResetPasswordSentIntent.resendResetLink() =
      ResendResetLinkIntent;
  const factory ResetPasswordSentIntent.timerTick() = TimerTickIntent;
  const factory ResetPasswordSentIntent.navigateBack() = NavigateBackIntent;
}
