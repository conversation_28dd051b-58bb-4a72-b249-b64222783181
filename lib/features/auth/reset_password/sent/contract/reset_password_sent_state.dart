import 'package:freezed_annotation/freezed_annotation.dart';

part 'reset_password_sent_state.freezed.dart';

@freezed
sealed class ResetPasswordSentState with _$ResetPasswordSentState {
  const factory ResetPasswordSentState({
    String? email,
    @Default(false) bool isLoading,
    @Default(false) bool isResendEnabled,
    @Default(120) int remainingSeconds,
  }) = _ResetPasswordSentState;

  const ResetPasswordSentState._();

  String get formattedRemainingTime {
    final minutes = remainingSeconds ~/ 60;
    final seconds = remainingSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
