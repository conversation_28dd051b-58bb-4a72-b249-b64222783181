import 'dart:async';

import 'package:cussme/domain/domain.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../data/use_cases/auth_use_case.dart';
import 'contract/reset_password_sent_contract.dart';

part 'reset_password_sent_presenter.g.dart';

@riverpod
class ResetPasswordSentPresenter extends _$ResetPasswordSentPresenter {
  final StreamController<ResetPasswordSentSideEffect> sideEffects =
      StreamController<ResetPasswordSentSideEffect>();
  late final AuthUseCase _authUseCase;
  Timer? _timer;

  @override
  ResetPasswordSentState build({String? email}) {
    _authUseCase = ref.read(authUseCaseProvider);
    state = ResetPasswordSentState(email: email);

    ref.onDispose(() {
      sideEffects.close();
      _timer?.cancel();
    });

    _startTimer();

    return state;
  }

  void intentHandler(ResetPasswordSentIntent intent) {
    switch (intent) {
      case ResendResetLinkIntent _:
        if (state.isResendEnabled) {
          _resendResetLink();
        }
        break;
      case TimerTickIntent _:
        _updateTimerTick();
        break;
      case NavigateBackIntent _:
        sideEffects.safeAdd(const NavigateBackSideEffect());
        break;
    }
  }

  void _startTimer() {
    const remainingSeconds = 120;
    state = state.copyWith(
      remainingSeconds: remainingSeconds,
      isResendEnabled: false,
    );

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      intentHandler(const ResetPasswordSentIntent.timerTick());
    });
  }

  void _updateTimerTick() {
    if (state.remainingSeconds > 0) {
      state = state.copyWith(remainingSeconds: state.remainingSeconds - 1);
    } else {
      _timer?.cancel();
      state = state.copyWith(isResendEnabled: true);
    }
  }

  Future<void> _resendResetLink() async {
    try {
      state = state.copyWith(isLoading: true);

      await _authUseCase.sendPasswordResetEmail(state.email);

      _startTimer();
    } on CussMeException catch (e) {
      sideEffects.safeAdd(ResetPasswordSentSideEffect.showError(e.toString()));
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}

@riverpod
Stream<ResetPasswordSentSideEffect> resetPasswordSentSideEffects(
    Ref ref, String? email) {
  final sideEffects = ref
      .read(resetPasswordSentPresenterProvider(email: email).notifier)
      .sideEffects;
  return sideEffects.stream;
}
