import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../localization/generated/l10n.dart';
import 'contract/reset_password_sent_contract.dart';
import 'reset_password_sent_presenter.dart';

class ResetPasswordSentScreen extends ConsumerWidget {
  const ResetPasswordSentScreen({super.key, this.email});

  final String? email;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final state = ref.watch(resetPasswordSentPresenterProvider(email: email));
    final presenter =
        ref.read(resetPasswordSentPresenterProvider(email: email).notifier);
    final str = Str.of(context);

    return AdMobScaffold(
      backgroundColor: Palette.whiteFa,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        toolbarHeight: 56 + 12, // Default height + top padding
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Padding(
          padding: const EdgeInsets.fromLTRB(8.0, 12.0, 8.0, 0.0),
          child: AppBar(
            backgroundColor: Colors.transparent,
            title: Text(
              str.resetPassword,
              style: TextStyles.headlineSmall.copyWith(
                color: Palette.onSurface,
              ),
            ),
            automaticallyImplyLeading: false,
            actions: [
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => presenter.intentHandler(
                    const ResetPasswordSentIntent.navigateBack()),
              ),
            ],
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: const Icon(
                Icons.mark_email_read_outlined,
                size: 48,
                color: Palette.success,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              str.passwordResetEmailSent,
              style: TextStyles.titleMedium.copyWith(
                color: Palette.onSurface,
              ),
              textAlign: TextAlign.start,
            ),
            const SizedBox(height: 8),
            Text(
              str.passwordResetCheckSpam,
              style: TextStyles.bodyMedium.copyWith(
                color: Palette.onSurface,
              ),
              textAlign: TextAlign.start,
            ),
            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 16),
            Text(
              str.didNotReceiveEmail,
              style: TextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: Palette.onSurface,
              ),
              textAlign: TextAlign.start,
            ),
            const SizedBox(height: 16),
            _buildResendButton(state, presenter, str),
          ],
        ),
      ),
    );
  }

  Widget _buildResendButton(ResetPasswordSentState state,
      ResetPasswordSentPresenter presenter, Str str) {
    final isEnabled = state.isResendEnabled;

    return SizedBox(
      height: 40,
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isEnabled
            ? () => presenter
                .intentHandler(const ResetPasswordSentIntent.resendResetLink())
            : null,
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isEnabled ? Palette.primary : Palette.disabledButtonBackground,
          foregroundColor: isEnabled
              ? Palette.onPrimary
              : Palette.primaryDark.withValues(alpha: 0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
          ),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 24),
        ),
        child: state.isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : Text(
                isEnabled
                    ? str.resendResetLink
                    : str.resendResetLinkTimer(state.formattedRemainingTime),
                style: TextStyles.labelLarge,
              ),
      ),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(
      resetPasswordSentSideEffectsProvider(email),
      (_, next) {
        next.whenData((sideEffect) {
          switch (sideEffect) {
            case NavigateBackSideEffect _:
              Navigator.pop(context);
              break;
            case final ShowErrorSideEffect intent:
              Toast.show(context, intent.message);
              break;
          }
        });
      },
    );
  }
}
