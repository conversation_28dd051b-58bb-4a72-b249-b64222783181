import 'package:freezed_annotation/freezed_annotation.dart';

part 'reset_password_new_side_effects.freezed.dart';

@freezed
sealed class ResetPasswordNewSideEffect with _$ResetPasswordNewSideEffect {
  const factory ResetPasswordNewSideEffect.navigateToResetPasswordSuccess() =
      NavigateToResetPasswordSuccessSideEffect;
  const factory ResetPasswordNewSideEffect.showError(String message) =
      ShowErrorSideEffect;
  const factory ResetPasswordNewSideEffect.hideKeyboard() =
      HideKeyboardSideEffect;
  const factory ResetPasswordNewSideEffect.navigateBack() =
      NavigateBackSideEffect;
}
