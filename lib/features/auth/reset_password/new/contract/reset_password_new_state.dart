import 'package:freezed_annotation/freezed_annotation.dart';

part 'reset_password_new_state.freezed.dart';

@freezed
sealed class ResetPasswordNewState with _$ResetPasswordNewState {
  const factory ResetPasswordNewState({
    @Default(null) String? token,
    @Default(null) String? errorDescription,
    @Default('') String password,
    @Default('') String confirmPassword,
    @Default(null) String? passwordError,
    @Default(null) String? confirmPasswordError,
    @Default(false) bool isLoading,
    @Default(false) bool isPasswordVisible,
    @Default(false) bool isConfirmPasswordVisible,
  }) = _ResetPasswordNewState;

  const ResetPasswordNewState._();

  bool get isForgetPassword => token != null || errorDescription != null;
}
