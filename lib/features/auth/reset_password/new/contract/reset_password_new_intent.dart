import 'package:freezed_annotation/freezed_annotation.dart';

part 'reset_password_new_intent.freezed.dart';

@freezed
sealed class ResetPasswordNewIntent with _$ResetPasswordNewIntent {
  const factory ResetPasswordNewIntent.passwordChanged(String password) =
      PasswordChangedIntent;
  const factory ResetPasswordNewIntent.confirmPasswordChanged(
      String confirmPassword) = ConfirmPasswordChangedIntent;
  const factory ResetPasswordNewIntent.togglePasswordVisibility() =
      TogglePasswordVisibilityIntent;
  const factory ResetPasswordNewIntent.toggleConfirmPasswordVisibility() =
      ToggleConfirmPasswordVisibilityIntent;
  const factory ResetPasswordNewIntent.submitNewPassword() =
      SubmitNewPasswordIntent;
  const factory ResetPasswordNewIntent.navigateBack() = NavigateBackIntent;
}
