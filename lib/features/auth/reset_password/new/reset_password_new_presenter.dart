import 'dart:async';

import 'package:cussme/data/use_cases/auth_use_case.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/reset_password_new_contract.dart';

part 'reset_password_new_presenter.g.dart';

@riverpod
class ResetPasswordNewPresenter extends _$ResetPasswordNewPresenter {
  final StreamController<ResetPasswordNewSideEffect> sideEffects =
      StreamController<ResetPasswordNewSideEffect>();

  late final AuthUseCase _authUseCase;

  @override
  ResetPasswordNewState build(ResetPasswordParams? params) {
    _authUseCase = ref.read(authUseCaseProvider);

    ref.onDispose(() {
      sideEffects.close();
    });

    state = ResetPasswordNewState(
      token: params?.token,
      errorDescription: params?.errorDescription,
    );

    _hasLinkErrorAndShow();

    return state;
  }

  void intentHandler(ResetPasswordNewIntent intent) {
    switch (intent) {
      case final PasswordChangedIntent intent:
        state = state.copyWith(
          password: intent.password,
          passwordError: null,
        );
        break;
      case final ConfirmPasswordChangedIntent intent:
        state = state.copyWith(
          confirmPassword: intent.confirmPassword,
          confirmPasswordError: null,
        );
        break;
      case TogglePasswordVisibilityIntent _:
        state = state.copyWith(
          isPasswordVisible: !state.isPasswordVisible,
        );
        break;
      case ToggleConfirmPasswordVisibilityIntent _:
        state = state.copyWith(
          isConfirmPasswordVisible: !state.isConfirmPasswordVisible,
        );
        break;
      case SubmitNewPasswordIntent _:
        _hideKeyboard();
        _submitNewPassword();
        break;
      case NavigateBackIntent _:
        sideEffects.safeAdd(const ResetPasswordNewSideEffect.navigateBack());
        break;
    }
  }

  void _hideKeyboard() {
    sideEffects.safeAdd(const ResetPasswordNewSideEffect.hideKeyboard());
  }

  bool _hasLinkErrorAndShow() {
    if (state.errorDescription == null) return false;
    sideEffects
        .add(ResetPasswordNewSideEffect.showError(state.errorDescription!));
    return true;
  }

  bool _validateInputs() {
    bool isValid = true;
    state = state.copyWith(passwordError: null, confirmPasswordError: null);

    final passwordError = Validators.validatePassword(state.password);
    if (passwordError != null) {
      state = state.copyWith(passwordError: passwordError);
      isValid = false;
    }

    final confirmPasswordError = Validators.validateConfirmPassword(
      state.password,
      state.confirmPassword,
    );
    if (confirmPasswordError != null) {
      state = state.copyWith(confirmPasswordError: confirmPasswordError);
      isValid = false;
    }

    return isValid;
  }

  Future<void> _submitNewPassword() async {
    if (_hasLinkErrorAndShow()) return;
    if (!_validateInputs()) return;

    try {
      state = state.copyWith(isLoading: true);

      await _authUseCase.updatePasswordAndSignOut(state.password, state.token);

      sideEffects.safeAdd(
          const ResetPasswordNewSideEffect.navigateToResetPasswordSuccess());
    } on CussMeException catch (e) {
      sideEffects.safeAdd(ResetPasswordNewSideEffect.showError(e.toString()));
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}

@riverpod
Stream<ResetPasswordNewSideEffect> resetPasswordNewSideEffects(
    Ref ref, ResetPasswordParams? params) {
  return ref
      .read(resetPasswordNewPresenterProvider(params).notifier)
      .sideEffects
      .stream;
}
