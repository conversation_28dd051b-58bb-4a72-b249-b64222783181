import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/keyboard_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'contract/reset_password_new_contract.dart';
import 'reset_password_new_presenter.dart';

class ResetPasswordNewScreen extends ConsumerWidget {
  const ResetPasswordNewScreen({
    super.key,
    this.params,
  });

  final ResetPasswordParams? params;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final presenter =
        ref.read(resetPasswordNewPresenterProvider(params).notifier);
    final state = ref.watch(resetPasswordNewPresenterProvider(params));

    return AdMobScaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          state.isForgetPassword
              ? _buildForgetPasswordHeader(context)
              : CussMeHeader(
                  title: Str.of(context).resetPassword,
                  titleIcon: Icons.lock_reset,
                  backButtonText: Str.of(context).accountSettingsLabel,
                  onBackPressed: () => presenter.intentHandler(
                    const ResetPasswordNewIntent.navigateBack(),
                  ),
                ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    Str.of(context).enterNewPassword,
                    style: TextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w500,
                      color: Palette.onSurface,
                    ),
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    Str.of(context).passwordRequirements,
                    style: TextStyles.bodyMedium.copyWith(
                      color: Palette.onSurface,
                    ),
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(height: 24),
                  CustomTextField(
                    label: Str.of(context).passwordLabel,
                    onChanged: (value) => presenter.intentHandler(
                      ResetPasswordNewIntent.passwordChanged(value),
                    ),
                    errorText: state.passwordError,
                    obscureText: !state.isPasswordVisible,
                    togglePasswordVisibility: true,
                    onToggleVisibility: () => presenter.intentHandler(
                      const ResetPasswordNewIntent.togglePasswordVisibility(),
                    ),
                    textInputAction: TextInputAction.next,
                  ),
                  const SizedBox(height: 12),
                  CustomTextField(
                    label: Str.of(context).confirmPasswordLabel,
                    onChanged: (value) => presenter.intentHandler(
                      ResetPasswordNewIntent.confirmPasswordChanged(value),
                    ),
                    errorText: state.confirmPasswordError,
                    obscureText: !state.isConfirmPasswordVisible,
                    togglePasswordVisibility: true,
                    onToggleVisibility: () => presenter.intentHandler(
                      const ResetPasswordNewIntent
                          .toggleConfirmPasswordVisibility(),
                    ),
                    textInputAction: TextInputAction.done,
                  ),
                  const SizedBox(height: 24),
                  PrimaryButton(
                    text: Str.of(context).confirmNewPassword,
                    onPressed: () => presenter.intentHandler(
                      const ResetPasswordNewIntent.submitNewPassword(),
                    ),
                    isLoading: state.isLoading,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Column _buildForgetPasswordHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 32),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            Str.of(context).resetPassword,
            style: TextStyles.headlineSmall.copyWith(
              color: Palette.onSurface,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(resetPasswordNewSideEffectsProvider(params), (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateToResetPasswordSuccessSideEffect():
            GoRouter.of(context).goToResetPasswordSuccess();
            break;
          case ShowErrorSideEffect(:final message):
            Toast.show(context, message);
            break;
          case HideKeyboardSideEffect():
            KeyboardUtils.hideKeyboard(context);
            break;
          case NavigateBackSideEffect():
            Navigator.of(context).pop();
            break;
        }
      });
    });
  }
}
