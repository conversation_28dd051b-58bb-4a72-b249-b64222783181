import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

class ResetPasswordSuccessScreen extends StatelessWidget {
  const ResetPasswordSuccessScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AdMobScaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 80),
              SvgPicture.asset(
                'assets/images/ic_reset_success.svg',
                width: 56,
                height: 56,
              ),
              const SizedBox(height: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    Str.of(context).passwordResetSuccessTitle,
                    style: TextStyles.titleMedium.copyWith(
                      color: Palette.onSurface,
                    ),
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    Str.of(context).passwordResetSuccessMessage,
                    style: TextStyles.bodyMedium.copyWith(
                      color: Palette.onSurface,
                    ),
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(height: 16),
                  PrimaryButton(
                    text: Str.of(context).goToSignIn,
                    onPressed: () => GoRouter.of(context).goToSignIn(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
