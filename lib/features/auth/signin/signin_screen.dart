import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'widgets/signin_form.dart';

class SignInScreen extends ConsumerWidget {
  final NavigationData? navigationData;

  const SignInScreen({super.key, this.navigationData});

  @override
  Widget build(BuildContext context, WidgetRef ref) => AdMobScaffold(
        body: SignInForm(navigationData: navigationData),
      );
}
