import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'widgets/signin_form.dart';

class SignInBottomSheetContent extends ConsumerWidget {
  final NavigationData? navigationData;
  final VoidCallback onSwitchToSignUp;
  final ScrollController scrollController;

  const SignInBottomSheetContent({
    super.key,
    this.navigationData,
    required this.onSwitchToSignUp,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) => AdMobScaffold(
        body: SignInForm(
          scrollController: scrollController,
          navigationData: navigationData,
          onSwitchToSignUp: onSwitchToSignUp,
          isBottomSheet: true,
        ),
      );
}
