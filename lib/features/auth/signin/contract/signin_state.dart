import 'package:cussme/routing/navigation_data.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'signin_state.freezed.dart';

@freezed
sealed class SignInState with _$SignInState {
  const factory SignInState({
    @Default('') String email,
    @Default('') String password,
    @Default(false) bool isPasswordVisible,
    @Default(false) bool isEmailLoading,
    @Default(false) bool isGoogleLoading,
    @Default(false) bool isAppleLoading,
    @Default(null) String? emailError,
    @Default(null) String? passwordError,
    @Default(null) NavigationData? navigationData,
  }) = _SignInState;
}
