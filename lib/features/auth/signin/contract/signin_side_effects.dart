import 'package:cussme/routing/navigation_data.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'signin_side_effects.freezed.dart';

@freezed
sealed class SignInSideEffect with _$SignInSideEffect {
  const factory SignInSideEffect.navigateToHome() = NavigateToHomeSideEffect;
  const factory SignInSideEffect.navigateToGuestHome() =
      NavigateToGuestHomeSideEffect;
  const factory SignInSideEffect.navigateToSignUp() =
      NavigateToSignUpSideEffect;
  const factory SignInSideEffect.navigateToForgotPassword() =
      NavigateToForgotPasswordSideEffect;
  const factory SignInSideEffect.navigateToIntro() = NavigateToIntroSideEffect;
  const factory SignInSideEffect.navigateToIntroWithDestination(
      NavigationData navigationData) = NavigateToIntroWithDestinationSideEffect;
  const factory SignInSideEffect.navigateToDestination(
      NavigationData navigationData) = NavigateToDestinationSideEffect;
  const factory SignInSideEffect.navigateToAppleWebView() =
      NavigateToAppleWebViewSideEffect;
  const factory SignInSideEffect.showError(String message) =
      ShowErrorSideEffect;
  const factory SignInSideEffect.hideKeyboard() = HideKeyboardSideEffect;
  const factory SignInSideEffect.openUrl(String url) = OpenUrlSideEffect;
}
