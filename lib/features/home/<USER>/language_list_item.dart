import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';

class LanguageListItem extends StatelessWidget {
  final LanguageEntity language;
  final VoidCallback onTap;

  const LanguageListItem({
    super.key,
    required this.language,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Palette.primaryContainer,
                borderRadius: BorderRadius.circular(100),
                border: Border.all(color: Palette.primary),
              ),
              alignment: Alignment.center,
              child: Text(
                language.name.firstLetter,
                style: TextStyles.titleMedium.copyWith(color: Palette.primary),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                language.name,
                style: TextStyles.titleMedium.copyWith(
                  color: Palette.black1d,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const Icon(
              Icons.arrow_right_sharp,
              color: Palette.onSurface,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }
}
