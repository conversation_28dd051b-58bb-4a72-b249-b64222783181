import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'contract/home_contract.dart';

part 'home_presenter.g.dart';

@riverpod
class HomePresenter extends _$HomePresenter {
  late StreamController<HomeSideEffect> sideEffects =
      StreamController<HomeSideEffect>();

  late final AuthUseCase _authUseCase;
  late final WordUseCase _wordUseCase;
  late final DataChangeTracker _dataChangeTracker;

  @override
  HomeState build({required Key key}) {
    _authUseCase = ref.read(authUseCaseProvider);
    _wordUseCase = ref.read(wordUseCaseProvider);
    _dataChangeTracker = ref.read(dataChangeTrackerProvider);

    final pageController = FreezablePageController(
      initialPage: 0,
      viewportFraction: 0.8,
    );

    ref.onDispose(() {
      sideEffects.close();
      pageController.dispose();
    });

    state = HomeState(
      loadingState: ScreenLoadingState.loading,
      pageController: pageController,
    );

    _checkIfUserIsGuest();
    _fetchHome();

    return state;
  }

  void intentHandler(HomeIntent intent) {
    switch (intent) {
      case final NavigateToWordListIntent intent:
        sideEffects.safeAdd(HomeSideEffect.navigateToWordList(intent.language));
        break;
      case RetryIntent _:
        _fetchHome();
        break;
      case NavigateToLanguagesIntent _:
        sideEffects.safeAdd(state.isGuest
            ? const HomeSideEffect.navigateToSignIn()
            : const HomeSideEffect.navigateToLanguages());
        break;

      case final WordClickedIntent intent:
        final index = intent.index;
        state = state.copyWith(currentWordIndex: index);
        state.pageController?.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        break;

      case final ChangeWordIndexIntent intent:
        final index = intent.index;
        state = state.copyWith(currentWordIndex: index);
        break;
      case final NavigateToWordDetailIntent intent:
        sideEffects.safeAdd(HomeSideEffect.navigateToWordDetail(intent.wordId));
        break;
      case ReturnedIntent _:
        if (_dataChangeTracker.shouldRefresh(
          key.toString(),
          TrackingFeatures.languages,
        )) {
          _fetchHome();
        }
        break;
      case final PlayPronunciationIntent intent:
        _playPronunciation(intent.word);
        break;
      case NavigateToSearchIntent _:
        sideEffects.safeAdd(const HomeSideEffect.navigateToSearch());
        break;
    }
  }

  Future<void> _playPronunciation(WordEntity word) async {
    try {
      await _wordUseCase.speak(word);
    } on PremiumRequiredException catch (e) {
      sideEffects.safeAdd(HomeSideEffect.navigateToPremium(e.isGuest));
    }
  }

  void _checkIfUserIsGuest() async {
    final isGuest = _authUseCase.isGuest();
    UserEntity? user;

    if (!isGuest) {
      user = await _authUseCase.getUpdatedUser();
    }

    state = state.copyWith(isGuest: isGuest, user: user);
  }

  Future<void> _fetchHome() async {
    try {
      state = state.copyWith(loadingState: ScreenLoadingState.loading);
      _dataChangeTracker.recordFetch(key.toString());

      final homeResponse = await _wordUseCase.getHome();

      state = state.copyWith(
        loadingState: ScreenLoadingState.loaded,
        languages: homeResponse.languages,
        words: homeResponse.words,
        selectedAllLanguages: homeResponse.selectedAllLanguages,
      );
    } catch (e) {
      state = state.copyWith(loadingState: ScreenLoadingState.error);
    }
  }
}

@riverpod
Stream<HomeSideEffect> homeSideEffects(Ref ref, {required Key key}) {
  return ref.watch(homePresenterProvider(key: key).notifier).sideEffects.stream;
}
