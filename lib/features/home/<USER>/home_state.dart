import 'package:cussme/domain/entities/language_entity.dart';
import 'package:cussme/domain/entities/user_entity.dart';
import 'package:cussme/domain/entities/word_entity.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'home_state.freezed.dart';

@freezed
sealed class HomeState with _$HomeState {
  const factory HomeState({
    @Default(false) bool isGuest,
    UserEntity? user,
    @Default([]) List<LanguageEntity> languages,
    @Default([]) List<WordEntity> words,
    @Default(false) bool selectedAllLanguages,
    @Default(0) int currentWordIndex,
    FreezablePageController? pageController,
    @Default(ScreenLoadingState.loading) ScreenLoadingState loadingState,
  }) = _HomeState;

  const HomeState._();

  bool get isPremium => user?.isPremium ?? false;
  bool get hasUserLoaded => user != null || isGuest;
}
