import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'home_intent.freezed.dart';

@freezed
sealed class HomeIntent with _$HomeIntent {
  const factory HomeIntent.retry() = RetryIntent;
  const factory HomeIntent.navigateToLanguages() = NavigateToLanguagesIntent;
  const factory HomeIntent.changeWordIndex(int index) = ChangeWordIndexIntent;
  const factory HomeIntent.wordClicked(int index) = WordClickedIntent;
  const factory HomeIntent.navigateToWordDetail(String wordId) =
      NavigateToWordDetailIntent;
  const factory HomeIntent.navigateToWordList(LanguageEntity language) =
      NavigateToWordListIntent;
  const factory HomeIntent.playPronunciation(WordEntity word) =
      PlayPronunciationIntent;
  const factory HomeIntent.returned() = ReturnedIntent;
  const factory HomeIntent.navigateToSearch() = NavigateToSearchIntent;
}
