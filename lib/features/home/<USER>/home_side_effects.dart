import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'home_side_effects.freezed.dart';

@freezed
sealed class HomeSideEffect with _$HomeSideEffect {
  const factory HomeSideEffect.showError(String message) = ShowErrorSideEffect;
  const factory HomeSideEffect.navigateToLanguages() =
      NavigateToLanguagesSideEffect;
  const factory HomeSideEffect.navigateToWordDetail(String wordId) =
      NavigateToWordDetailSideEffect;
  const factory HomeSideEffect.navigateToSignIn() = NavigateToSignInSideEffect;
  const factory HomeSideEffect.navigateToWordList(LanguageEntity language) =
      NavigateToWordListSideEffect;
  const factory HomeSideEffect.navigateToSearch() = NavigateToSearchSideEffect;
  const factory HomeSideEffect.navigateToPremium(bool isGuest) =
      NavigateToPremiumSideEffect;
}
