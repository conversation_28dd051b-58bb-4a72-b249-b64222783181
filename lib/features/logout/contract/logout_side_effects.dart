import 'package:freezed_annotation/freezed_annotation.dart';

part 'logout_side_effects.freezed.dart';

@freezed
sealed class LogoutSideEffect with _$LogoutSideEffect {
  const factory LogoutSideEffect.navigateToSignIn() =
      NavigateToSignInSideEffect;
  const factory LogoutSideEffect.showError(String message) =
      ShowErrorSideEffect;
  const factory LogoutSideEffect.closeDialog() = CloseDialogSideEffect;
}
