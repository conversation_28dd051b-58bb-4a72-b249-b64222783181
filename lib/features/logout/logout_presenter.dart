import 'dart:async';

import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../data/use_cases/auth_use_case.dart';
import 'contract/logout_contract.dart';

part 'logout_presenter.g.dart';

@riverpod
class LogoutPresenter extends _$LogoutPresenter {
  final StreamController<LogoutSideEffect> sideEffects =
      StreamController<LogoutSideEffect>();
  late final AuthUseCase _authUseCase;

  @override
  LogoutState build() {
    _authUseCase = ref.read(authUseCaseProvider);
    ref.onDispose(() => sideEffects.close());
    return const LogoutState();
  }

  void intentHandler(LogoutIntent intent) {
    switch (intent) {
      case ConfirmLogoutIntent _:
        _logout();
        break;
      case CancelLogoutIntent _:
        sideEffects.safeAdd(const LogoutSideEffect.closeDialog());
        break;
    }
  }

  Future<void> _logout() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _authUseCase.signOut();
    } catch (e) {
      sideEffects.safeAdd(LogoutSideEffect.showError(e.toString()));
      sideEffects.safeAdd(const LogoutSideEffect.closeDialog());
    }
  }
}

@riverpod
Stream<LogoutSideEffect> logoutSideEffects(Ref ref) {
  return ref.read(logoutPresenterProvider.notifier).sideEffects.stream;
}
