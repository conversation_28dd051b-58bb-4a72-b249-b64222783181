import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../localization/generated/l10n.dart';
import '../../routing/app_router.dart';
import '../../ui/ui.dart';
import 'contract/logout_contract.dart';
import 'logout_presenter.dart';

class LogoutDialog extends ConsumerWidget {
  const LogoutDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final state = ref.watch(logoutPresenterProvider);
    final presenter = ref.read(logoutPresenterProvider.notifier);

    return Dialog(
      backgroundColor: Palette.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(28),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Str.of(context).logoutDialogTitle,
                  style: TextStyles.headlineSmall.copyWith(
                    color: Palette.onSurface,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  Str.of(context).logoutDialogMessage,
                  style: TextStyles.bodyMedium.copyWith(
                    color: Palette.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: state.isLoading
                          ? null
                          : () => presenter
                              .intentHandler(const LogoutIntent.cancel()),
                      style: TextButton.styleFrom(
                        foregroundColor: Palette.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                      child: Text(
                        Str.of(context).cancel,
                        style: TextStyles.labelLarge,
                      ),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: state.isLoading
                          ? null
                          : () => presenter.intentHandler(
                              const LogoutIntent.confirmLogout()),
                      style: TextButton.styleFrom(
                        foregroundColor: Palette.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                      child: state.isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Palette.primary,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              Str.of(context).logout,
                              style: TextStyles.labelLarge.copyWith(
                                color: Palette.primary,
                              ),
                            ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

void _handleSideEffects(BuildContext context, WidgetRef ref) {
  ref.listen(logoutSideEffectsProvider, (_, next) {
    next.whenData((sideEffect) {
      switch (sideEffect) {
        case NavigateToSignInSideEffect _:
          GoRouter.of(context).goToSignIn();
          break;
        case final ShowErrorSideEffect intent:
          Toast.show(context, intent.message);
          break;
        case CloseDialogSideEffect _:
          Navigator.of(context).pop();
          break;
      }
    });
  });
}

void showLogoutDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const LogoutDialog(),
  );
}
