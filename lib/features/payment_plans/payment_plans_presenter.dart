import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/payment_plans_contract.dart';

part 'payment_plans_presenter.g.dart';

@riverpod
class PaymentPlansPresenter extends _$PaymentPlansPresenter {
  final StreamController<PaymentPlansSideEffect> sideEffects =
      StreamController<PaymentPlansSideEffect>();

  late SubscriptionUseCase _subscriptionUseCase;

  @override
  PaymentPlansState build() {
    _subscriptionUseCase = ref.read(subscriptionUseCaseProvider);
    listenToVerificationResult();

    ref.onDispose(() {
      sideEffects.close();
      _subscriptionUseCase.dispose();
    });

    return PaymentPlansState(
      plans: SubscriptionPlan.getPlans(),
      selectedPlan: SubscriptionPlan.getAnnualPlan(),
    );
  }

  void intentHandler(PaymentPlansIntent intent) {
    switch (intent) {
      case final PlanSelectedIntent intent:
        state = state.copyWith(selectedPlan: intent.plan);
        break;
      case ContinuePressedIntent _:
        _handlePurchase();
        break;
      case RestorePressedIntent _:
        _handleRestore();
        break;
      case ClosePressedIntent _:
        sideEffects.safeAdd(const PaymentPlansSideEffect.navigateBack());
        break;
    }
  }

  Future<void> _handlePurchase() async {
    if (state.isLoading) return;

    try {
      state = state.copyWith(
          isLoading: true, currentAction: CurrentAction.purchase);

      await _subscriptionUseCase
          .purchaseSubscription(state.selectedPlan.productId);
    } catch (e) {
      state = state.copyWith(isLoading: false);
      sideEffects.safeAdd(PaymentPlansSideEffect.showMessage(e.toString()));
    }
  }

  Future<void> _handleRestore() async {
    if (state.isLoading) return;

    try {
      state =
          state.copyWith(isLoading: true, currentAction: CurrentAction.restore);

      await _subscriptionUseCase.restorePurchases();
    } catch (e) {
      state = state.copyWith(isLoading: false);
      sideEffects.safeAdd(PaymentPlansSideEffect.showMessage(e.toString()));
    }
  }

  void listenToVerificationResult() {
    ref.listen(verificationResultStreamProvider, (_, next) {
      next.whenData((result) {
        _handleVerificationResult(result);
      });
    });
  }

  void _handleVerificationResult(VerificationResult result) {
    if (!state.isLoading) return;

    state = state.copyWith(isLoading: false);

    switch (result) {
      case VerificationCancelled _:
        break;
      case final VerificationSuccess vs:
        if (state.currentAction == CurrentAction.restore &&
            !vs.response.isUserPremium) {
          sideEffects.safeAdd(
              PaymentPlansSideEffect.showMessage(Str.current.restoreFailed));
          return;
        }

        if (state.currentAction == CurrentAction.purchase) {
          sideEffects.safeAdd(
              const PaymentPlansSideEffect.showSubscriptionSuccessDialog());
        } else {
          sideEffects.safeAdd(
              PaymentPlansSideEffect.showMessage(Str.current.restoreSuccess));
          sideEffects.safeAdd(const PaymentPlansSideEffect.navigateToHome());
        }
        break;
      case VerificationError(:final message):
        sideEffects.safeAdd(PaymentPlansSideEffect.showMessage(message));
        break;
      case VerificationSupabaseFailed _:
        sideEffects.safeAdd(PaymentPlansSideEffect.showMessage(
            Str.current.notCompletePurchase));
        state = state.copyWith(currentAction: CurrentAction.supabaseFailed);
        break;
    }
  }
}

@riverpod
Stream<PaymentPlansSideEffect> paymentPlansSideEffects(Ref ref) {
  return ref.read(paymentPlansPresenterProvider.notifier).sideEffects.stream;
}
