import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_plans_side_effects.freezed.dart';

@freezed
sealed class PaymentPlansSideEffect with _$PaymentPlansSideEffect {
  const factory PaymentPlansSideEffect.navigateBack() = NavigateBackSideEffect;
  const factory PaymentPlansSideEffect.navigateToHome() =
      NavigateToHomeSideEffect;
  const factory PaymentPlansSideEffect.showMessage(String message) =
      ShowErrorSideEffect;
  const factory PaymentPlansSideEffect.showSubscriptionSuccessDialog() =
      ShowSubscriptionSuccessDialogSideEffect;
}
