import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_plans_intent.freezed.dart';

@freezed
sealed class PaymentPlansIntent with _$PaymentPlansIntent {
  const factory PaymentPlansIntent.planSelected(SubscriptionPlan plan) =
      PlanSelectedIntent;
  const factory PaymentPlansIntent.continuePressed() = ContinuePressedIntent;
  const factory PaymentPlansIntent.restorePressed() = RestorePressedIntent;
  const factory PaymentPlansIntent.closePressed() = ClosePressedIntent;
}
