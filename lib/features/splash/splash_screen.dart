import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../data/use_cases/auth_use_case.dart';
import '../../routing/app_router.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with SingleTickerProviderStateMixin {
  late final AnimationController _progressAnimation;

  @override
  void initState() {
    super.initState();
    _progressAnimation = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat();

    _progressAnimation.forward().then((_) async {
      final authUseCase = ref.read(authUseCaseProvider);
      final shouldNavigateHome = await authUseCase.shouldNavigateHome();

      if (mounted) {
        if (shouldNavigateHome) {
          GoRouter.of(context).goToHome();
        } else {
          GoRouter.of(context).goToSignIn();
        }
      }
    });
  }

  @override
  void dispose() {
    _progressAnimation.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: Palette.surface,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 54),
              child: SvgPicture.asset(
                'assets/images/splash_logo.svg',
                width: 267,
                height: 217,
              ),
            ),
            const SizedBox(height: 24),
            SvgPicture.asset('assets/images/splash_subtitle.svg'),
            const SizedBox(height: 105),
            SizedBox(
              width: screenWidth / 3,
              child: AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: LinearProgressIndicator(
                      value: _progressAnimation.value,
                      backgroundColor: Palette.surfaceVariant,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Palette.primary,
                      ),
                      minHeight: 8,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
