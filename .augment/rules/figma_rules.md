# Accuracy Requirements

```yaml
- Layout: ±0.5px margin/padding tolerance
- Colors: 100% HEX match via Palette class
- Typography:
  - ±0.5sp size accuracy
  - Exact font family/weight
  - ±1px letter spacing
- Assets:
  - 3x resolution validation
  - Exact aspect ratio preservation
```

# Implementation Rules

## 1. Color Handling

```dart
// ❌ Bad - Hardcoded
Container(color: Color(0xFF2A2A2A));

// ✅ Good - Palette constant
Container(color: Palette.primaryDark);

// Required Palette Structure
class Palette {
  static const Color primaryDark = Color(0xFF2A2A2A);
  // Add all Figma colors as constants
}
```

## 2. Text Styling

- Use **TextTheme** when:
  - Style is reused in 3+ locations
  - Matches Material Design guidelines

- Use **direct styles** for:
  - Unique text combinations
  - Exact Figma-specific measurements
  - Ignore height and letter spacing fields for text style

- Create `text_styles.dart` for custom combinations.

## 3. Asset Handling

| Asset Type   | Pre-Check Action              | Resolution Path               |
|--------------|--------------------------------|--------------------------------|
| Images       | Verify 3x resolution exists    | Request @3x.png from designer  |
| Fonts        | Check family/weight match      | Get exact .ttf files           |
| Icons        | Confirm vector format          | Request SVG files              |

# File Structure

```plaintext
lib/
└── ui/
    ├── styles/
    │   ├── palette.dart
    │   └── text_styles.dart
    └── widgets/
        └── [component_name]/
            ├── [widget].dart
            └── [widget]_styles.dart
```

# Workflow

## Phase 1: Precision Implementation
- Analyze Figma frame with Dev Mode metrics
- Implement raw layout with exact values
- Verify against Figma measurements

## Phase 2: Style Organization (After accuracy confirmation)
- Identify repeated styles
- Move colors to `Palette` class
- Create shared text styles only when reused

## Phase 3: Final Verification

| Figma Element | Code Implementation | Variance | Status  |
|---------------|----------------------|----------|---------|
| Header text   | 16sp Inter Bold        | 0sp      | ✅ Pass |
| Button padding| 16.0px vs 16.5px       | +0.5px   | ⚠️ Warn |

# Mandatory Checks

- No hardcoded colors in widgets.
- No hardcoded font sizes unless unique.
- Validate image resolutions (must have @3x).
- Maintain font weights exactly.
- Preserve aspect ratios for all assets.

# Accuracy Threshold

| Category    | Tolerance       |
|-------------|-----------------|
| Layout      | ±0.5px         |
| Font Size   | ±0.5sp         |
| Letter Spacing | ±1px       |
| Colors      | Exact HEX match  |
| Assets      | 3x resolution and aspect ratio preserved |

# Notes

- Always use Figma Dev Mode for inspecting measurements.
- Communicate early if assets are missing or inaccurate.
- Treat warnings (e.g., ±0.5px) seriously if repeated.
- Final deliverable must match Figma design with no visible difference.
