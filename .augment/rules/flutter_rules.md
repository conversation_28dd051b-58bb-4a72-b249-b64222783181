You are an expert in Flutter, Dart, Riverpod, Freezed, Testing, MVI, Clean Arch, GoRouter, and Supabase.

## UI & Widgets
- **Widgets:**
  - Use Flutter's built-in widgets and create custom widgets as needed.
  - Use `const` constructors wherever possible.
  - Avoid deeply nested widget trees. A flatter structure improves build performance, readability, and maintainability.
  - Break down large widgets into smaller, focused and reusable components.
  - Implement list view optimizations (e.g., ListView.builder).
  - In `TextField` widgets, configure `textCapitalization`, `keyboardType`, and `textInputAction` appropriately.
- **Responsiveness:**
  - Design layouts for multiple screen sizes using `LayoutBuilder` or `MediaQuery`.
- **Style:**
  - Use themes for consistent styling across the app.
  - Prefer using `Theme.of(context).textTheme.titleLarge` (instead of deprecated names like `headline6`).

## Localization Guidelines

### String Management
- **NO hardcoded strings** allowed in the codebase
- All strings MUST be defined in ARB files under `lib/localization/`
- Base locale file: `intl_en.arb` (English)

### ARB File Structure
- **Keys:**
  - Use semantic naming: `{feature}{component}{purpose}`
  - Examples: `loginButtonSubmit`, `profileLabelName`
  - Keep keys in camelCase
- **Metadata:**
  - Dscription is not needed
  - Document placeholder usage
  - Specify plural/select rules when applicable

Example ARB format:
```json
{
  "@@locale": "en",
  
  "loginButtonSubmit": "Sign In",

  "profileWelcomeMessage": "Welcome {name}",
  "@profileWelcomeMessage": {
    "placeholders": {
      "name": {
        "type": "String",
        "example": "John"
      }
    }
  },

  "inboxMessageCount": "{count, plural, =0{No messages} =1{1 message} other{{count} messages}}",
  "@inboxMessageCount": {
    "placeholders": {
      "count": {
        "type": "int",
        "example": "5"
      }
    }
  }
}
```

### Usage in Code
- **Access Pattern:** Always use `Str.of(context).keyName` or you can use `Str.current.keyName` whenever is needed (mostly where context is not available)
- **NEVER** use string literals in UI code
- **ALWAYS** use string resources for:
  - UI text
  - Error messages
  - Toast/Snackbar messages
  - Dialog content
  - Button labels
  - Hints and placeholders
  - Screen titles
  - Menu items
  - UI related text in presenter

### Examples:
```dart
// ❌ WRONG
Text("Welcome back!")
TextField(hint: "Enter email")
ElevatedButton(child: Text("Submit"))

// ✅ CORRECT
Text(Str.of(context).login_welcome_message)
TextField(hint: Str.of(context).login_field_email_hint)
ElevatedButton(child: Text(Str.of(context).login_button_submit))
```

### Workflow
1. **Adding New Strings:**
   - Add string to `intl_en.arb`
   - Run generation command: `flutter pub run intl_utils:generate`
   - Use generated accessor in code

2. **String Updates:**
   - Modify string in ARB file only
   - Never modify generated files
   - Run generation command after changes

3. **Code Review:**
   - Check for hardcoded strings
   - Verify proper string key naming
   - Ensure descriptions are meaningful
   - Validate placeholder usage

### Testing
- Include string key verification in widget tests
- Test plural and parameter variations
- Verify string loading for supported locales

### Maintenance
- Regular audit for unused strings
- Consistent key naming across features
- Documentation of special format strings
- Version control for string changes

## Development & Coding Practices
- **Debugging & Logging:**
  - Use `log` for debugging instead of `print`.
- **Code Generation & Formatting:**
  - Use `build_runner` to generate code for Freezed and Riverpod.
  - Use arrow functions for simple one‑line functions.
  - Prefer trailing commas for better formatting.
  - Always declare types for variables and functions; avoid using `any`.
  - Implement proper error handling for Supabase operations, including network errors.
- **Imports:**
  - Use package imports for files in `lib/` (avoid relative imports).
- **Naming & Conventions:**
  - **Classes:** Use PascalCase.
  - **Variables & Functions:** Use camelCase.
  - **Files & Directories:** Use underscores (e.g., `my_widget.dart`).
  - **Constants:** Define and use constant values (avoid magic numbers).
  - **Function Naming:** Start each function with a verb (e.g., `fetchData()`).
  - **Boolean Naming:** Use verb phrases (e.g., `isLoading`, `hasError`).
  - **Consistency:** Follow naming conventions (e.g., _repository.dart for interfaces, _repository_impl.dart for implementations).

## Code Practices & Principles
- **Lint & Code Review:**
  - Run lint tools and refactor until no issues remain.
  - Dont use deprecated codes. Replace them with another code or approach.
- **SOLID & Clean Code:**
  - Follow SOLID principles for maintainability and scalability.
  - Write concise, readable, and simple code.
  - Avoid over-engineering; choose the simplest solution that works.
  - Avoid nesting blocks by using early returns and utility functions.
  - Use higher-order functions (map, filter, reduce) to simplify code.
  - Consider extracting common logic into mixins, such as input validation and error handling.
- **Immutability & Functional Patterns:**
  - Prefer immutability for data structures.
  - Use `const` for literals that do not change.
  - Employ functional and declarative programming patterns when appropriate.
  - Favor composition over inheritance.

## State Management & Testing
- **State Management:**
  - Prefer stateless widgets where possible.
  - Use `ConsumerWidget` with Riverpod for state-dependent widgets.
  - Use stateful widgets only when local state (e.g., animations, form controllers) cannot be managed effectively with Riverpod.
  - Use `HookConsumerWidget` when combining Riverpod and Flutter Hooks.
- **Testing:**
  - Follow Test-Driven Development (TDD): write unit tests first, then code.
  - Use the Arrange-Act-Assert (AAA) pattern in tests.
  - Write unit tests for all business logic and critical UI components.
  - Use test doubles (fakes or mocks) to simulate dependencies.
  - Ensure coverage for both happy paths and edge cases.
  - Name test variables clearly. Follow the convention: inputX, mockX, actualX, expectedX, etc.
  - Use mocktail for simpler mocking instead of mockito.
  - Implement golden tests for UI verification.
  - Introduce snapshot testing for state changes over time.
- **Error Handling:**
  - Implement proper error handling for asynchronous operations and API calls.

## Miscellaneous Best Practices
- Optimize for Flutter performance metrics (first meaningful paint, time to interactive).
- Profile performance with Flutter DevTools to identify bottlenecks (e.g., slow builds or layouts).
- Always check for the latest updates from official Flutter, Riverpod, and Supabase documentation.
- Document complex logic and non‑obvious decisions.
- Avoid hardcoding sensitive data (e.g., API keys); use environment variables with flutter_dotenv.
- Sanitize user inputs before sending them to Supabase or APIs.
- Pin dependency versions in pubspec.yaml and review updates monthly to avoid breaking changes
- Optimize network calls and caching strategies especially when using Supabase.
- Profile with Flutter DevTools to catch build performance issues, and consider using keys in widgets where necessary.

## Architectural Pattern
project uses combination of mvi pattern, clean architecture, and repository pattern.

**MVI**
This guide provides a comprehensive, step-by-step approach to implementing a new feature in a Flutter project using the MVI (Model-View-Intent) pattern with Riverpod for state management. Follow these instructions to maintain consistency with the existing login feature architecture.

### 1. Define the State

**File:** `lib/features/<feature>/contract/<feature>_state.dart`

**Purpose:** Define an immutable state class that holds all data required for the UI to render.

- Use the Freezed package to ensure immutability and generate utility methods like `copyWith`.
- Include fields for inputs, errors, loading states, sub-flow indicators, and UI-specific flags.

**Example:**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part '<feature>_state.freezed.dart';

@freezed
sealed class FeatureState with _$FeatureState {
  const factory FeatureState({
    @Default(null) String? inputField,
    @Default(null) String? inputError,
    @Default(null) String? generalError,
    @Default(false) bool isLoading,
    @Default(false) bool isSubFlowActive,
    @Default(false) bool isButtonEnabled,
  }) = _FeatureState;
}
```

**Note:** Run `flutter pub run build_runner build` to generate the Freezed code.

---

### 2. Define Intents

**File:** `lib/features/<feature>/contract/<feature>_intent.dart`

**Purpose:** Define a sealed class for intents, representing all possible user actions.

**Example:**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part '<feature>_intent.freezed.dart';

@freezed
sealed class FeatureIntent with _$FeatureIntent {
  const factory FeatureIntent.submit() = SubmitIntent;
  const factory FeatureIntent.inputChanged(String newValue) = InputChangedIntent;
}
```

### 3. Define Side Effects

**File:** `lib/features/<feature>/contract/<feature>_side_effects.dart`

**Purpose:** Define a sealed class for side effects, representing outcomes that affect the app globally (e.g., navigation).

**Example:**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part '<feature>_side_effects.freezed.dart';

@freezed
sealed class FeatureSideEffect with _$FeatureSideEffect {
  const factory FeatureSideEffect.navigateToNextScreen() = NavigateToNextScreenSideEffect;
}
```

---

### 4. Create a Contract File

**File:** `lib/features/<feature>/contract/<feature>_contract.dart`

**Purpose:** Export all contract-related files for convenience.

**Example:**

```dart
export '<feature>_intent.dart';
export '<feature>_side_effects.dart';
export '<feature>_state.dart';
```

---

### 5. Create the Presenter

**File:** `lib/features/<feature>/<feature>_presenter.dart`

**Purpose:** Manage business logic, process intents, update state, and emit side effects.

**Example:**

```dart
import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'contract/<feature>_contract.dart';

part '<feature>_presenter.g.dart';

@riverpod
class FeaturePresenter extends _$FeaturePresenter {
  late final SomeUseCase _someUseCase;
  StreamController<FeatureSideEffect> sideEffects = StreamController<FeatureSideEffect>();
  Timer? _timer;

  @override
  FeatureState build() {
    _someUseCase = ref.read(someUseCaseProvider);
    ref.onDispose(() {
      sideEffects.close();
      _timer?.cancel();
    });
    return const FeatureState();
  }
  
  void intentHandler(FeatureIntent intent) {
  switch (intent) {
    case SubmitIntent _:
      if (_validateInput()) {
        _submit();
      }
      break;
     case EmailChangedIntent intent:
        _email = intent.email;
        _updateButtonStateIfNeeded();
        break;
  }
}

  bool _validateInput() {
    state = state.copyWith(inputError: null);
    if (state.inputField == null || state.inputField!.isEmpty) {
      state = state.copyWith(inputError: 'Input is required');
      return false;
    }
    return true;
  }

  void _submit() async {
    try {
      state = state.copyWith(isLoading: true, generalError: null);
      await _someUseCase.doSomething();
      sideEffects.safeAdd(const FeatureSideEffect.navigateToNextScreen());
    } catch (e) {
      _showError('An error occurred');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  void _updateButtonState() {
    state = state.copyWith(isButtonEnabled: state.inputField != null && state.inputField!.isNotEmpty);
  }

  void _showError(String error) {
    state = state.copyWith(generalError: error);
    _timer?.cancel();
    _timer = Timer(const Duration(seconds: 5), () {
      state = state.copyWith(generalError: null);
    });
  }
}
```

---

### 6. Create the View

**File:** `lib/features/<feature>/<feature>_screen.dart`

**Purpose:** Build the UI based on the state and handle side effects.

**Example:**

```dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'contract/<feature>_contract.dart';

class FeatureScreen extends ConsumerWidget {
  const FeatureScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final FeatureState state = ref.watch(featurePresenterProvider);
    final presenter = ref.read(featurePresenterProvider.notifier);
    
    return Scaffold(
      body: Column(
        children: [
          TextField(
            onChanged: (value) => presenter.intentHandler(InputChangedIntent(value)),
          ),
          ElevatedButton(
            onPressed: state.isButtonEnabled ? () => presenter.intentHandler(SubmitIntent()) : null,
            child: state.isLoading ? CircularProgressIndicator() : Text('Submit'),
          ),
          if (state.inputError != null) Text(state.inputError!),
          if (state.generalError != null) Text(state.generalError!),
        ],
      ),
    );
  }
}
```
- **Repository Pattern:**
- Separate data access interfaces from their concrete implementations.
- Implement error handling for Supabase and other API operations (including network errors).
- **Data Layer:**
- Keep remote (API) and local (caching/storage) operations separate.
- Use proper dependency injection (riverpod) for repository implementations.

## Navigation & Routing
- **Routing:**
- Use GoRouter (or auto_route) for navigation and deep linking.
- Use named routes instead of relying on path directly for better maintainability.
- Configure routes in a centralized location for easy management and modification.


## Directory Structure
- **Project Layout:**
project/
├── assets/                # Static resources like images, fonts, etc.
├── coverage/              # Reports for code coverage analysis
├── integration_test/      # Integration test files
├── lib/                   # Core application source code
│   ├── di/                # Dependency injection configuration (e.g., using get_it or provider)
│   ├── localization/      # Localization setup for multi-language support
│   ├── utils/             # Helper functions and utility classes
│   ├── data/              # Data layer for managing data sources and business logic
│   │   ├── remote/        # Contains Dio configuration, API endpoint constants, and interceptors.
│   │   │   ├── dio_configuration.dart      # Dio client setup for HTTP requests
│   │   │   ├── uri_constant.dart          # API endpoint constants
│   │   │   ├── remote_params.dart         # Parameters for remote requests
│   │   │   └── interceptors/              # Custom interceptors for API calls
│   │   │       ├── sample_interceptor.dart # custome http bearer
│   │   │       └── ...                    # Other implementations
│   │   ├── shared_preferences/            # Local storage using SharedPreferences
│   │   │   └── pref_constants.dart        # Constants for SharedPreferences keys
│   │   ├── use_cases/            # Defines business logic encapsulated in use case classes.
│   │   ├── repositories/                  # Houses abstract repository interfaces and their implementations
│   │   │   ├── sample_repository.dart       # Interface for sample operations
│   │   │   ├── ...                    # Other repositories
│   │   │   ├── impl/                      # Subdirectory for concrete repository implementations.
│   │   │       ├── sapmple_repository_impl.dart    # Sample repository implementation
│   │   │       └── ...                    # Other implementations
│   │   └── data.dart        # A barrel file exporting all necessary components from the data layer.
│   ├── ui/                # Shared UI components for consistency across the app
│   │   ├── widgets/       # Reusable widgets (e.g., buttons, text fields)
│   │   ├── styles/        # Text styles and theme configurations
│       └── ui.dart        # A barrel file exporting all necessary components from the ui layer.
│   ├── features/       # Features using the MVI pattern
│   │   ├── fuature_a/          # fuature_a with MVI implementation
│   │   │   ├── contract/              # MVI-specific components
│   │   │   │   ├── fuature_a_state.dart     # Model: Defines the UI state (e.g., loading, success)
│   │   │   │   ├── fuature_a_intent.dart    # Intent: User actions (e.g., "submit login")
│   │   │   │   └── fuature_a_side_effects.dart # Side effects (e.g., navigation, error messages)
│   │   │   ├── fuature_a_presenter.dart     # Presenter: Manages state and processes intents
│   │   │   └── fuature_a_screen.dart        # View: UI widget displaying state and capturing intents
│   │   │   └── ...        # Other features
│   ├── routing/           # Navigation setup (e.g., using Go Router)
│   └── domain/            # Business logic entities
│   │   ├── entities/        # Data entities (e.g., User, Client)
│   │   ├── request/       # API request models
│   │   ├── response/      # API response models
│       └── domain.dart        # A barrel file exporting all necessary components from the domain layer.
├── test/                  # Unit and widget tests
├── test_driver/           # Drivers for integration tests
├── android/                   # Files for android deployment
└── ios/                   # Files for ios deployment

## Data Layer

### Barrel File (e.g `data.dart`)
A single export file to streamline imports.
```dart
export 'shared_preferences/pref_constants.dart';
export 'repositories/auth_repository.dart';
export 'repositories/impl/auth_repository_impl.dart';
export 'remote/dio_configuration.dart';
export 'remote/uri_constant.dart';
export 'use_cases/auth_use_case.dart';
```

### Shared Preferences (`shared_preferences/pref_constants.dart`)
Define constants for keys to avoid hardcoding strings.
```dart
class PrefConstants {
  static const String token = 'TOKEN';
  static const String refreshToken = 'REFRESH_TOKEN';
}
```

### Remote Configuration (`remote/`)

#### Dio Configuration (`dio_configuration.dart`)
Configure Dio with base options and interceptors, provided via Riverpod.
```dart
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:your_project/data/remote/uri_constant.dart';
import 'package:your_project/data/remote/bearer_interceptor.dart';
import 'package:your_project/data/remote/token_interceptor.dart';
import 'package:your_project/data/use_cases/auth_use_case.dart';

part 'dio_configuration.g.dart';

@riverpod
Dio dio(DioRef ref) {
  final dio = Dio()
    ..options.baseUrl = UrlConstants.baseUrl
    ..options.contentType = 'application/json'
    ..options.connectTimeout = const Duration(seconds: 60)
    ..options.receiveTimeout = const Duration(seconds: 40);

  final authUseCase = ref.read(authUseCaseProvider);
  dio.interceptors.add(BearerInterceptor(authUseCase));
  dio.interceptors.add(TokenInterceptor(authUseCase, dio));

  return dio;
}
```

#### API Endpoints (`uri_constant.dart`)
Centralize endpoint URLs.
```dart
class UrlConstants {
  static String dynamicBaseUrl = '${Uri.base.origin}/api';
  static String baseUrl = const String.fromEnvironment('BASE_URL').isNotEmpty
      ? '${const String.fromEnvironment('BASE_URL')}/api'
      : dynamicBaseUrl;

  static const String login = '/auth/login';
  static const String logout = '/auth/logout';
}
```

### Repositories (`repositories/`)

#### Abstract Repository (`auth_repository.dart`)
Define the interface and provide it via Riverpod.
```dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:your_project/domain/domain.dart';

part 'auth_repository.g.dart';

abstract class AuthRepository {
  FutureOr<LoginResponse?> login(LoginRequest req);
  FutureOr<void> logout(LogoutRequest req);
  void saveTokens(String token, String refreshToken);
  String? getToken();
  void removeToken();
}

@riverpod
AuthRepository authRepository(AuthRepositoryRef ref) {
  final sharedPrefs = ref.read(sharedPreferencesProvider);
  final dio = ref.read(dioProvider);
  return AuthRepositoryImpl(sharedPrefs, dio);
}
```

#### Implementation (`impl/auth_repository_impl.dart`)
Implement the repository using Dio and SharedPreferences.
```dart
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:your_project/data/remote/uri_constant.dart';
import 'package:your_project/data/shared_preferences/pref_constants.dart';
import 'package:your_project/domain/domain.dart';

class AuthRepositoryImpl extends AuthRepository {
  final SharedPreferences sharedPreferences;
  final Dio dio;

  AuthRepositoryImpl(this.sharedPreferences, this.dio);

  @override
  FutureOr<LoginResponse?> login(LoginRequest req) async {
    final response = await dio.post<Map<String, dynamic>>(
      UrlConstants.login,
      data: req.toJson(),
    );
    return response.data != null ? LoginResponse.fromJson(response.data!) : null;
  }

  @override
  FutureOr<void> logout(LogoutRequest req) async {
    await dio.post(UrlConstants.logout, data: req.toJson());
  }

  @override
  void saveTokens(String token, String refreshToken) {
    sharedPreferences.setString(PrefConstants.token, token);
    sharedPreferences.setString(PrefConstants.refreshToken, refreshToken);
  }

  @override
  String? getToken() => sharedPreferences.getString(PrefConstants.token);

  @override
  void removeToken() {
    sharedPreferences.remove(PrefConstants.token);
    sharedPreferences.remove(PrefConstants.refreshToken);
  }
}
```

### Use Cases (`use_cases/`)

#### Use Case (`auth_use_case.dart`)
Encapsulate business logic and provide it via Riverpod.
```dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:your_project/data/repositories/auth_repository.dart';
import 'package:your_project/domain/domain.dart';

part 'auth_use_case.g.dart';

class AuthUseCase {
  final AuthRepository authRepository;

  AuthUseCase(this.authRepository);

  FutureOr<LoginResponse?> login(LoginRequest loginRequest) async {
    final response = await authRepository.login(loginRequest);
    if (response != null) {
      authRepository.saveTokens(response.token, response.refreshToken!);
    }
    return response;
  }

  FutureOr<void> logout() async {
    final token = authRepository.getToken();
    await authRepository.logout(LogoutRequest(token: token!));
    authRepository.removeToken();
  }

  String? getToken() => authRepository.getToken();
}

@riverpod
AuthUseCase authUseCase(AuthUseCaseRef ref) {
  final authRepository = ref.read(authRepositoryProvider);
  return AuthUseCase(authRepository);
}
```

## Best Practices

- **Constants**: Centralize URLs and preference keys in `UrlConstants` and `PrefConstants`.
- **Dio**: Configure with interceptors and provide via Riverpod.
- **Repositories**: Use abstract interfaces with separate implementations.
- **Use Cases**: Keep business logic separate from data access.
- **Riverpod**: Annotate injectable classes with `@riverpod` for code generation.
- **Errors**: Handle failures with `DioException` or `null` returns.

## Important Notes
- ALWAYS run ```flutter pub run build_runner build``` when needed automatically.
- If there is a shared widget, place it under ui/widgets. naming rules provided before.
- NEVER import a class that is under ui or data directories directly. always add class to ui.dart or data.dart and then import those classes.

